/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, model, Schema, Types } from 'mongoose';
import { StakeholderGroup, StakeholderGroupSchema } from './stakeholderGroup';
import { ActionList, DataPeriods } from '../service/utr/constants';
import { ObjectId } from 'bson';
import { UserPlain } from './user';
import { UniversalTrackerMin, UniversalTrackerPlain } from './universalTracker';
import { CommonUtrv } from './commonUtr';
import { EscalationPolicy, EscalationPolicySchema } from './escalationPolicy';
import { stakeholdersAggregation } from '../repository/aggregations';
import { UtrValueType, ValueAggregation, ValueValidation } from "./public/universalTrackerType";
import { SourceTypes, UniversalTrackerValuePublic, ValueData, ValueDataSource, ValueDataSourceType } from "./public/universalTrackerValueType";
import { InitiativePlain } from './initiative';
import { LatestUtrvNotesSchema, UtrvNote } from './common/utrvNote';
import { SurveyModelPlain } from './survey';
import { assuranceFieldsSchema } from "./common/assurance";
import { PartialAssuranceField } from '../service/assurance/model/Assurance';

export enum NotApplicableTypes {
  NA = 'not_applicable',
  NR = 'not_reported',
}

export interface IpLocation {
  ip: string;
  latitude?: number;
  longitude?: number;
}

export enum UtrvAssuranceStatus {
  /** Question is added to assurance portfolio, but not yet completed **/
  Created = 'created',
  /** Question is completed and should no longer be updated **/
  Completed = 'completed',
  /** Question has been marked to be explicitly open for modifications **/
  CompletedOpen = 'completed_open',
  Rejected = 'rejected',
  /** Question has been marked to be explicitly open and has been modified **/
  Restated = 'restated',
  /** Question has been marked to be partial assurance and can assure later **/
  Partial = 'partial',
}

/**
 * These are representing metrics that have had assured actions
 */
export const assuredUtrvActionStatuses = [
  UtrvAssuranceStatus.Partial,
  UtrvAssuranceStatus.Completed,
  UtrvAssuranceStatus.Restated,
  UtrvAssuranceStatus.Rejected,
];

export const getAssuranceText = (status?: UtrvAssuranceStatus) => {
  switch (status) {
    case UtrvAssuranceStatus.Created:
      return 'Selected for Assurance';
    case UtrvAssuranceStatus.Completed:
    case UtrvAssuranceStatus.CompletedOpen:
      return 'Assured';
    case UtrvAssuranceStatus.Rejected:
      return 'Disputed';
    case UtrvAssuranceStatus.Restated:
      return 'Restated';
    case UtrvAssuranceStatus.Partial:
      return 'Partially assured';
    default:
      return '';
  }
}

export type EvidenceData<T = Types.ObjectId> = {
  documentId: T;
  description?: string;
}

export interface ValueHistory<T = Types.ObjectId, U = number> extends Partial<Pick<UtrvNote, 'note' | 'editorState'>>{
  _id?: T;
  action: string;
  userId: T;
  value: U | undefined;
  date?: Date;
  evidence?: T[];
  evidenceData?: EvidenceData<T>[];
  unit?: string;
  numberScale?: string;
  assuranceStatus?: UtrvAssuranceStatus;
  utrvAssuranceId?: T;
  assuranceFields?: PartialAssuranceField[];
  valueType?: UtrValueType;
  valueData?: ValueData;
  location?: IpLocation;
}

export interface UtrvAction<T = Types.ObjectId> {
  _id: T;
  status: string;
  value?: any;
  valueData?: ValueData;
  effectiveDate: Date;
  lastUpdated: Date;
  instructions?: string;
  stakeholders: StakeholderGroup;
  universalTrackerId: T;
  initiativeId: T;
  users: UserPlain[];
  escalationPolicy?: EscalationPolicy;
  universalTracker: UniversalTrackerMin;
  daysOverdue?: number;
  history?: ValueHistory[];
}

const valueDataSourceSchema = new Schema<ValueDataSource>(
  {
    type: {
      type: Schema.Types.String,
      enum: Object.values(ValueDataSourceType),
      required: true,
    },
    data: Schema.Types.Mixed,
  },
  { _id: false }
);

const columnSchema = new Schema({
  code: { type: Schema.Types.String, required: true },
  value: {
    type: Schema.Types.Mixed,
    required: false,
    // Currently only string array is supported from valueList
    set: (value: null | undefined | number | string | string[]) => {
      if (typeof value === 'number' && !Number.isFinite(value)) {
        return undefined;
      }
      return value;
    }
  },
  unit: Schema.Types.String,
  numberScale: Schema.Types.String,
  source: { type: valueDataSourceSchema },
}, { _id: false });

const tableSchemaProp = {
  table: [
    [columnSchema]
  ],
};

const inputSchema = new Schema({
  data: Schema.Types.Mixed,
  unit: Schema.Types.String,
  value: {
    type: Schema.Types.Number,
    set: (value: string | number | undefined | null) => Number.isFinite(Number(value)) ? value : undefined
  },
  numberScale: Schema.Types.String,
  valueChainPercentage: {
    type: Schema.Types.Number,
    set: (value: string | number | undefined | null) => Number.isFinite(Number(value)) ? value : undefined
  },
  source: {
    type: [valueDataSourceSchema],
    required: false,
    default: undefined,
  },
  ...tableSchemaProp,
}, { _id: false });

const ValueDataSchema = new Schema(
  {
    data: Schema.Types.Mixed,
    isImported: Schema.Types.Boolean,
    explain: Schema.Types.String,
    notApplicableType: {
      type: Schema.Types.String,
      enum: [NotApplicableTypes.NA, NotApplicableTypes.NR],
      required: false,
    },
    ...tableSchemaProp,
    input: { type: inputSchema, required: false },
    source: {
      type: [valueDataSourceSchema],
      required: false,
      default: undefined,
    },
  },
  { _id: false, strict: false }
);

const valueTypeParams = {
  valueType: {
    type: String,
    enum: [
      UtrValueType.Number,
      UtrValueType.Sample,
      UtrValueType.Percentage,
      UtrValueType.Text,
      UtrValueType.Date,
      UtrValueType.ValueListMulti,
      UtrValueType.ValueList,
      UtrValueType.NumericValueList,
      UtrValueType.TextValueList,
      UtrValueType.Table,
    ],
  },
}

export const evidenceDataSchema = new Schema<EvidenceData>({
  documentId: { type: Schema.Types.ObjectId, required: true },
  description: Schema.Types.String,
}, { _id: false });

const assuranceStatus = {
  type: Schema.Types.String,
  enum: Object.values(UtrvAssuranceStatus)
};
const UniversalTrackerValueHistorySchema = new Schema<ValueHistory>({
  action: {
    type: String,
    enum: [ActionList.Created, ActionList.Updated, ActionList.Verified, ActionList.Rejected],
  },
  unit: Schema.Types.String,
  numberScale: Schema.Types.String,
  userId: Schema.Types.ObjectId,
  date: { type: Date, default: Date.now },
  value: Number,
  valueData: { type: ValueDataSchema, required: false },
  ...valueTypeParams,
  location: {
    ip: Schema.Types.String,
    latitude: Schema.Types.Number,
    longitude: Schema.Types.Number,
  },
  note: Schema.Types.String,
  editorState: Schema.Types.Mixed,
  assuranceStatus,
  utrvAssuranceId: Schema.Types.ObjectId,
  assuranceFields: assuranceFieldsSchema,
  evidence: [Schema.Types.ObjectId],
  evidenceData: { type: [evidenceDataSchema], default: undefined },
});

const commonCompositeDataDef = {
  fragmentUtrvs: { type: [Schema.Types.ObjectId], required: true },
  surveyId: { type: Schema.Types.ObjectId, required: true },
  blueprint: { type: Schema.Types.String, required: true },
  configCode: { type: Schema.Types.String, required: false },
};
const SecondarySchema = new Schema(commonCompositeDataDef, { _id: false });

const CompositeDataSchema = new Schema({
  ...commonCompositeDataDef,
  secondary: [SecondarySchema],
}, { _id: false });

export const utrvValidTypes = ['actual', 'baseline', 'target'];

const sourceItemSchema = new Schema<SourceItem>({
  utrvId: { type: Schema.Types.ObjectId, required: true },
  // Expect only utrvs that have history with updated entry as part of this
  historyId: { type: Schema.Types.ObjectId, required: true },
  latestStatus: {
    type: Schema.Types.String,
    // Backward compatibility - assume it was verified.
    required: false,
    enum: Object.values(ActionList)
  },
  latestHistoryId: { type: Schema.Types.ObjectId, required: false },
}, { _id: false });

export const UniversalTrackerValueSchema = new Schema<UniversalTrackerValuePlain>({
  universalTrackerId: { type: Schema.Types.ObjectId, required: true },
  ...valueTypeParams,
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  unit: Schema.Types.String,
  numberScale: Schema.Types.String,
  sourceType: {
    type: String,
    enum: Object.values(SourceTypes),
    default: SourceTypes.Manual,
    required: true
  },
  sourceCode: String,
  effectiveDate: { type: Date, default: Date.now, required: true },
  period: {
    type: Schema.Types.String,
    enum: DataPeriods,
    default: DataPeriods.Yearly
  },
  type: {
    type: String,
    enum: utrvValidTypes,
    required: true
  },

  created: { type: Date, default: Date.now },
  lastUpdated: { type: Date, default: Date.now },
  deletedDate: Schema.Types.Date,

  value: {
    type: Number,
    set: (value: string | number) => Number.isFinite(Number(value)) ? value : undefined
  },

  valueData: { type: ValueDataSchema, required: false },

  note: { type: Schema.Types.String, required: false },
  notes: LatestUtrvNotesSchema,
  assuranceStatus,
  evidence: [Schema.Types.ObjectId],
  evidenceData: { type: [evidenceDataSchema], default: undefined },

  instructions: { type: String, required: false },
  evidenceRequired: { type: Boolean, default: false, required: true },
  noteRequired: { type: Boolean, default: false },
  verificationRequired: { type: Boolean, default: true, required: true },
  isPrivate: Schema.Types.Boolean,
  stakeholders: { type: StakeholderGroupSchema, required: false },
  escalationPolicy: { type: EscalationPolicySchema, required: false },
  history: [UniversalTrackerValueHistorySchema],
  status: {
    type: String,
    enum: [ActionList.Created, ActionList.Updated, ActionList.Verified, ActionList.Rejected],
    default: ActionList.Created
  },
  compositeData: { type: CompositeDataSchema, required: false },
  sourceItems: { type: [sourceItemSchema], required: false, default: undefined },
  valueAggregation: {
    trim: true,
    type: Schema.Types.String,
    enum: Object.values(ValueAggregation),
    required: false,
    set: function (value: any) {
      return value === '' ? undefined : value;
    },
  },
}, { collection: 'universal-tracker-values' });

interface SecondaryComposite<T = Types.ObjectId> {
  fragmentUtrvs: T[];
  surveyId?: T;
  blueprint?: string;
  configCode?: string;
}

export interface CompositeData<T = Types.ObjectId> extends SecondaryComposite<T> {
  secondary?: SecondaryComposite[];
}
export interface CompositeDataSurveyId {
  compositeData?: { surveyId?: ObjectId }
}

export interface SourceItem<T = Types.ObjectId> {
  utrvId: T;
  /** points to the last action=updated history, where data was taken from **/
  historyId: T;

  /** points to the last history when the source item was used **/
  latestHistoryId: T | undefined;
  /** for convenience, we pull the status of the utrv at the time of use **/
  latestStatus: string | undefined;
}

export interface UniversalTrackerValuePlain<T = Types.ObjectId, U = UniversalTrackerPlain> extends UniversalTrackerValuePublic<T, U, ValueHistory<T>>, CommonUtrv<T> {
  _id: any;
  effectiveDate: Date;
  period?: DataPeriods;
  deletedDate?: Date;
  history: ValueHistory<T>[];
  assuranceStatus?: UtrvAssuranceStatus;
  value: number | undefined;
  lastUpdated: Date;
  compositeData?: CompositeData<T>;
  sourceItems?: SourceItem<T>[];
  valueAggregation?: ValueAggregation;
  initiative?: InitiativePlain;
  survey?: SurveyModelPlain;
}

export interface UniversalTrackerValueExtended extends UniversalTrackerValuePlain {
  universalTracker: UniversalTrackerPlain;
}

export interface UniversalTrackerValueAggregated extends UniversalTrackerValueExtended {
  valueValidation: ValueValidation,
  sourceType: SourceTypes.Aggregated;
}

export type UniversalTrackerValueModel = HydratedDocument<UniversalTrackerValuePlain>;

export interface UniversalTrackerValueModelExtended extends UniversalTrackerValueModel {
  universalTracker: UniversalTrackerPlain;
}

UniversalTrackerValueSchema.index({ status: 1, initiativeId: 1 }, { unique: false });
UniversalTrackerValueSchema.index({ initiativeId: 1, universalTrackerId: 1, effectiveDate: 1 }, { unique: false });
UniversalTrackerValueSchema.index({ status: 1, sourceType: 1, stakeholders: 1 }, { unique: false });
UniversalTrackerValueSchema.index({ initiativeId: 1, status: 1, universalTrackerId: 1, effectiveDate: 1 }, { unique: false });
UniversalTrackerValueSchema.index({ effectiveDate: 1, type: 1, initiativeId: 1, universalTrackerId: 1 }, { unique: false });
UniversalTrackerValueSchema.index({ stakeholder: 1, status: 1 }, { unique: false });
UniversalTrackerValueSchema.index({ lastUpdated: 1 }, { unique: false });
UniversalTrackerValueSchema.index({ 'compositeData.surveyId': 1, 'compositeData.fragmentUtrvs': 1 }, { unique: false });

// User actions
UniversalTrackerValueSchema.index({
  'stakeholders': 1,
  'compositeData.surveyId': 1,
  'deletedDate': 1,
  'sourceType': 1,
}, { name: 'survey_user_action_lookup' });

UniversalTrackerValueSchema.index({
  'stakeholders.stakeholder': 1,
  'deletedDate': 1,
  'sourceType': 1,
}, { name: 'source_type_by_stakeholder_source_type' });

UniversalTrackerValueSchema.index({
  'stakeholders.verifier': 1,
  'deletedDate': 1,
  'sourceType': 1,
}, { name: 'source_type_by_verifier_source_type' });

UniversalTrackerValueSchema.index({
  'sourceType': 1,
  'deletedDate': 1,
  'compositeData.surveyId': 1,
}, { name: 'sourceType_by_composite_data_survey_id' });

UniversalTrackerValueSchema.index({
  'universalTrackerId': 1,
  'compositeData.surveyId': 1,
}, { name: 'scorecard_by_survey_id' });

UniversalTrackerValueSchema.index({
  'deletedDate': 1,
  'compositeData.fragmentUtrvs': 1,
}, { name: 'get_composite_by_fragment_id' });

UniversalTrackerValueSchema.index({
  'deletedDate': 1,
  'compositeData.secondary.fragmentUtrvs': 1,
}, { name: 'get_composite_by_secondary_fragment_id' });

UniversalTrackerValueSchema.index({
  'initiativeId': 1,
  'universalTrackerId': 1,
  'status': 1,
  'type': 1,
  'deletedDate': 1
}, { name: 'mongo_atlas_recommendation_1' });

UniversalTrackerValueSchema.index({
  'initiativeId': 1,
  'universalTrackerId': 1,
  'effectiveDate': 1
}, { name: 'mongo_atlas_recommendation_2' });

UniversalTrackerValueSchema.index({
  universalTrackerId: 1,
  initiativeId: 1,
  effectiveDate: 1,
  deletedDate: 1,
}, { name: 'get_survey_process_referenced_data' });

UniversalTrackerValueSchema.virtual('universalTracker', {
  ref: 'UniversalTracker', // The model to use
  localField: 'universalTrackerId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

UniversalTrackerValueSchema.virtual('initiative', {
  ref: 'Initiative', // The model to use
  localField: 'initiativeId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

UniversalTrackerValueSchema.virtual('survey', {
  ref: 'Survey',
  localField: 'compositeData.surveyId',
  foreignField: '_id',
  justOne: true,
});


UniversalTrackerValueSchema.virtual('ledgerUniversalTrackerValue', {
  ref: 'LedgerUniversalTrackerValue',
  localField: '_id',
  foreignField: 'utrvId',
  justOne: true,
});

const UniversalTrackerValue = model('UniversalTrackerValue', UniversalTrackerValueSchema);

if (process.env.DB_SYNC_INDEXES) {
  UniversalTrackerValue.syncIndexes({}).catch((e) => console.error(e));
}

UniversalTrackerValueSchema.statics.getStakeholders = (utrvId: string) => {
  const aggregations = stakeholdersAggregation({ _id: new ObjectId(utrvId) });
  return UniversalTrackerValue.aggregate(aggregations);
};

export default UniversalTrackerValue;
