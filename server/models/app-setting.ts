import { model, Schema } from "mongoose";

export enum AppSettingKey {
  OpenAIRelevantUTRsDocumentScanAssistantId = 'openai-document-utrs-assistant-id',
  OpenAIUTRsFileId = 'openai-utrs-file-id',
}

export interface AppSettingPlain {
  key: string;
  value: string;
  description?: string;
  created: Date;
  updated: Date;
}

const AppSettingSchema = new Schema<AppSettingPlain>(
  {
    key: { type: Schema.Types.String, required: true, trim: true, unique: true },
    value: { type: Schema.Types.String, required: true, trim: true },
    description: Schema.Types.String,
  },
  {
    timestamps: { createdAt: 'created', updatedAt: 'updated' },
  }
);

export const AppSetting = model('AppSetting', AppSettingSchema, 'app-settings');