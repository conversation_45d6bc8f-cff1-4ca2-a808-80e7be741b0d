/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { AsyncLocalStorage } from 'async_hooks';
import { Request, RequestHandler } from 'express';
import uaParser from 'ua-parser-js';
import { CreateAuditEntry, GeographicalContext, Operation } from '../../service/audit/AuditModels';
import { generatedUUID } from '../../service/crypto/token';
import { LEVEL } from '../../service/event/Events';
import { AuthenticatedRequest } from '../../http/AuthRouter';
import { getLocationInfo, isExtendedLocation } from '../../service/location/LocationService';
import { DomainConfig } from '../../service/organization/domainConfig';
import { getDomainConfigRepository } from '../../service/organization/DomainConfigRepository';
import { getSubdomain } from '../../util/url';
import { wwgLogger } from '../../service/wwgLogger';
import ContextError from '../../error/ContextError';

type BaseContext = Pick<
  CreateAuditEntry,
  'actor' | 'transaction' | 'debugContext' | 'client' | 'operation' | 'severity'
> &
  Partial<CreateAuditEntry>;

export interface RequestContext extends BaseContext {
  requestId: string;
  ip: string | undefined;
  domainConfig?: DomainConfig;
  origin?: string;
}

export const asyncRequestContext = new AsyncLocalStorage<RequestContext>();

const repo = getDomainConfigRepository();

export function operationFromRequest(method: string) {
  switch (method) {
    case 'delete':
      return Operation.Remove;
    case 'post':
      return Operation.Create;
    case 'put':
    case 'patch':
      return Operation.Modify;
    case 'get':
      return Operation.Access;
    default:
      return Operation.Access;
  }
}

const getGeoContextFromIp = async (ipAddress: string | undefined): Promise<GeographicalContext | undefined> => {
  if (!ipAddress) {
    return undefined;
  }

  const location = await getLocationInfo(ipAddress);
  if (isExtendedLocation(location)) {
    return {
      country: location.country,
      geolocation: {
        lat: location.latitude,
        lon: location.longitude,
      },
      postalCode: location.postalCode,
      state: location.state,
      city: location.city,
      continentCode: location.continentCode,
    };
  }
};

export const getDebugContext = (req: Request, requestId: string) => ({
  debugData: {
    requestId,
    requestUri: req.path,
    url: req.originalUrl,
  },
});

export const fromRequest = async (req: AuthenticatedRequest) => {
  const requestId = generatedUUID();
  const ipAddress = req.ip;
  const rawUserAgent = req.header('user-agent') ?? '';
  const ua = uaParser(rawUserAgent);
  const method = req.method.toLowerCase();
  const user = req.user;

  if (!user) {
    throw new ContextError(`user must be authenticated for audit logs`);
  }

  const origin = req.header('origin');
  const subdomain = getSubdomain(origin);

  const context: RequestContext = {
    debugContext: getDebugContext(req, requestId),
    actor: {
      id: user._id,
      type: 'User',
      alternateId: user.oktaUserId,
    },
    client: {
      userAgent: {
        rawUserAgent: rawUserAgent,
        os: ua.os.name,
        browser: ua.browser.name,
      },
      device: ua.device.type,
      id: undefined,
      ipAddress: ipAddress,
      geographicalContext: await getGeoContextFromIp(ipAddress),
    },
    operation: operationFromRequest(method),
    severity: method !== 'delete' ? LEVEL.INFO : LEVEL.NOTICE,
    transaction: {
      id: requestId,
      type: 'WEB',
    },
    origin,
    domainConfig: subdomain ? await repo.getBySubDomain(subdomain) : undefined,
    ip: ipAddress,
    requestId: requestId,
  };
  return context;
};

export const ContextMiddleware: RequestHandler = (req, res, next) => {
  fromRequest(req as AuthenticatedRequest)
    .then((ctx) => asyncRequestContext.run(ctx, next))
    .catch((error) => {
      next(error);
    });
};
export const getContext = () => {
  const context = asyncRequestContext.getStore();
  if (!context) {
    // This indicates a logical error if called where context is expected
    throw new ContextError('Request context not found. Ensure you are within a ContextMiddleware chain.');
  }
  return context;
};

export const tryGetContext = () => {
  try {
    return getContext();
  } catch (e) {
    wwgLogger.error(e)
  }
}
