/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import { AssurancePortfolioExpandedExtra, AssurancePortfolioPlain } from '../service/assurance/model/AssurancePortfolio';
import { UniversalTrackerValueAssurancePlain } from '../service/assurance/model/Assurance';
import { OrganizationPermissionsMin, OrganizationPlain } from '../models/organization';
import { ConnectionUtr, UniversalTrackerBlueprintMin } from '../models/universalTracker';
import { KeysEnum } from "../models/public/projectionUtils";
import { InitiativeMin } from '../models/public/initiativeType';
import { SurveyActionDataAggregation, SurveyActionMinimalUtrv, SurveyModelMinData, SurveyModelMinWithUtrvsPlain, SurveyModelPlain } from '../models/survey';
import { InitiativePlain } from "../models/initiative";

export const userExcludedFields = {
  passwordHash: 0,
  dashboardPermissions: 0,
  isStaff: 0,
  permissions: 0,
  partnerTypes: 0,
  stakeholderTypes: 0,
  created: 0,
  mobileNumber: 0,
  telephoneNumber: 0,
  _v: 0,
};

export const initiativeMinFields: KeysEnum<InitiativeMin> = {
  _id: 1,
  name: 1,
  profile: 1,
  code: 1,
  startDate: 1,
  endDate: 1,
  type: 1,
  parentId: 1,
  industry: 1,
  industryText: 1,
  dataShare: 1,
};

export type SurveyModelMinimalInitiative = Pick<InitiativePlain,
  '_id' | 'name' | 'customer' | 'referrals' | 'permissionGroup' | 'type' | 'tags' | 'materialityMap'
>
export const initiativeSurveyMin: KeysEnum<SurveyModelMinimalInitiative> = {
  _id: 1,
  name: 1,
  type: 1,
  customer: 1,
  materialityMap: 1,
  permissionGroup: 1,
  referrals: 1,
  tags: 1,
};

export const surveyMinProjection: KeysEnum<SurveyModelMinData, 1> = {
  type: 1,
  initiativeId: 1,
  period: 1,
  effectiveDate: 1,
  utrvType: 1,
  evidenceRequired: 1,
  noteRequired: 1,
  noteInstructions: 1,
  noteInstructionsEditorState: 1,
  sourceItems: 1,
  verificationRequired: 1,
  isPrivate: 1,
  visibleStakeholders: 1,
  visibleUtrvs: 1,
  stakeholders: 1,
  code: 1,
  name: 1,
  sourceName: 1,
  unitConfig: 1,
  scope: 1,
  delegationScope: 1,
  roles: 1,
  permissions: 1,
  permissionGroup: 1,
  deadlineDate: 1,
  scheduledDates: 1,
  creatorId: 1,
  completedDate: 1,
  assessmentType: 1,
};

export const surveyWithUtrvsProjection: KeysEnum<SurveyModelMinWithUtrvsPlain, 1> = {
  ...surveyMinProjection,
  fragmentUniversalTrackerValues: 1,
  created: 1,
}

export const surveyBundleData = {
  _id: 1,
  code: 1,
  name: 1,
  sourceName: 1,
  effectiveDate: 1,
  unitConfig: 1,
};

export const assuranceSurvey: KeysEnum<AssurancePortfolioExpandedExtra['survey']> = {
  _id: 1,
  effectiveDate: 1,
  code: 1,
  name: 1,
  sourceName: 1,
  deletedDate: 1,
  roles: 1,
  visibleUtrvs: 1,
  verificationRequired: 1,
  initiativeId: 1
}

export type SurveySummary = Pick<SurveyModelPlain,
  '_id' |
  'name' |
  'initiativeId' |
  'period' |
  'effectiveDate' |
  'stakeholders' |
  'sourceName' |
  'completedDate' |
  'aggregatedDate' |
  'aggregatedVersion' |
  'type'
>;

export const surveySummary: KeysEnum<SurveySummary> = {
  _id: 1,
  name: 1,
  initiativeId: 1,
  period: 1,
  effectiveDate: 1,
  stakeholders: 1,
  sourceName: 1,
  completedDate: 1,
  aggregatedDate: 1,
  aggregatedVersion: 1,
  type: 1,
};


type SurveyActionMinimalUtrvKeys =
  // Remove composite, as it's getting re-added directly
  KeysEnum<Omit<SurveyActionMinimalUtrv, 'compositeData'>>
  & { "compositeData.surveyId": 0 | 1 };

export const surveyActionMinimalUtrvProjection: SurveyActionMinimalUtrvKeys = {
  _id: 1,
  universalTrackerId: 1,
  initiativeId: 1,
  valueData: 1,
  value: 1,
  status: 1,
  stakeholders: 1,
  assuranceStatus: 1,
  lastUpdated: 1,
  evidenceRequired: 1,
  noteRequired: 1,
  verificationRequired: 1,
  isPrivate: 1,
  effectiveDate: 1,
  "compositeData.surveyId": 1,
  notes: 1,
  sourceItems: 1,
}

export const surveyActionData: KeysEnum<SurveyActionDataAggregation> = {
  _id: 1,
  code: 1,
  delegationScope: 1,
  effectiveDate: 1,
  evidenceRequired: 1,
  noteRequired: 1,
  noteInstructions: 1,
  noteInstructionsEditorState: 1,
  initiativeId: 1,
  name: 1,
  period: 1,
  roles: 1,
  scope: 1,
  sourceName: 1,
  stakeholders: 1,
  type: 1,
  unitConfig: 1,
  utrvType: 1,
  verificationRequired: 1,
  displayPreferences: 1,

  created: 1,
  completedDate: 1,
  aggregatedDate: 1,

  fragmentUniversalTrackerValues: surveyActionMinimalUtrvProjection,

  initiatives: 1,
  list: 1,

  assessmentType: 1,

  /// Removed before returning
  blueprint: 1,
  fragmentUniversalTracker: 1,
  ignoredDate: 1,
  aggregatedSurveys: 1,
  filters: 1,
}

export const organizationProjectFields: KeysEnum<OrganizationPlain> = {
  _id: 1,
  name: 1,
  organizationType: 1,
  partnerTypes: 1,
  profile: 1,
  address: 1,
  code: 1,
  created: 1,
  permissions: 1,
};

const stakeholderUser = {
  _id: 1,
  firstName: 1,
  surname: 1,
  active: 1,
};

export const stakeholdersUsers = {
  _id: 1,
  initiativeId: 1,
  stakeholder: stakeholderUser,
  verifier: stakeholderUser,
  escalation: stakeholderUser,
};

// This should be very close to UtrvAction interface
export const actionProjection = {
  _id: 1,
  status: 1,
  universalTrackerId: 1,
  initiativeId: 1,
  stakeholders: 1,
  escalationPolicy: 1,
  effectiveDate: 1,
  lastUpdated: 1,
  instructions: 1,
  value: 1,
  type: 1,
  users: {
    _id: 1,
    firstName: 1,
    surname: 1,
    email: 1,
    profile: 1,
  },
};

export const assurancePortfolioProjection: KeysEnum<AssurancePortfolioPlain> = {
  _id: 1,
  created: 1,
  description: 1,
  history: 1,
  initiativeId: 1,
  portfolioType: 1,
  organizationId: 1,
  permissions: 1,
  status: 1,
  surveyId: 1,
  title: 1,
  documents: 1,
};

export const utrvAssuranceProjection: KeysEnum<UniversalTrackerValueAssurancePlain> = {
  utrvHistoryIndex: 1,
  utrvId: 1,
  _id: 1,
  created: 1,
  history: 1,
  stakeholders: 1,
  portfolioId: 1,
  status: 1,
  permissions: 1,
  partialFields: 1,
};

export const valueValidationProjection = {
  valueValidation: {
    min: 1,
    max: 1,
    table: 1,
    valueList: {
      type: 1,
      listId: 1,
      custom: 1,
      list: { $arrayElemAt: ['$valueList.options', 0] }
    }
  },
};

export const metricUnitProjection = {
  metricUnit: { $arrayElemAt: ['$metricUnit', 0] }
};

export const universalTrackerFields = {
  _id: 1,
  code: 1,
  type: 1,
  created: 1,
  description: 1,
  typeCode: 1,
  targetDirection: 1,
  valueLabel: 1,
  valueType: 1,
  unit: 1,
  unitType: 1,
  name: 1,
  instructions: 1,
  numberScale: 1,
};

export const connectUtrFields: KeysEnum<ConnectionUtr> = {
  _id: 1,
  code: 1,
  name: 1,
  valueLabel: 1,
  type: 1,
  valueType: 1,
  valueValidation: 1,
  typeCode: 1,
  unit: 1,
  unitType: 1,
  numberScale: 1,
}

export const universalTrackerPlainFields = {
  _id: 1,
  code: 1,
  type: 1,
  name: 1,
  alternatives: 1,
  ownerId: 1,
  tags: 1,
  valueType: 1,
  created: 1,
  unitType: 1,
  unit: 1,
  valueValidation: 1,
  targetDirection: 1,
  valueListOrdered: 1,
  valueListTargets: 1,
  metricUnit: 1,
  valueAggregation: 1,
  calculation: 1,
}

export const utrForBlueprintProject: KeysEnum<UniversalTrackerBlueprintMin> = {
  _id: 1,
  ownerId: 1,
  alternatives: 1,
  code: 1,
  name: 1,
  instructions: 1,
  tags: 1,
  type: 1,
  typeCode: 1,
  typeTags: 1,
  unitType: 1,
  unit: 1,
  numberScale: 1,
  valueLabel: 1,
  valueType: 1,
  valueValidation: 1,
};

export type utrvAggregation = Pick<UniversalTrackerValuePlain,
  '_id' |
  'status' |
  'universalTrackerId' |
  'initiativeId' |
  'type' |
  'effectiveDate' |
  'value' |
  'valueData' |
  'assuranceStatus' |
  'compositeData' |
  'lastUpdated' |
  'stakeholders' |
  'valueType' |
  'period'
  >

export const universalTrackerValueAggregation: KeysEnum<utrvAggregation> = {
  _id: 1,
  status: 1,
  universalTrackerId: 1,
  initiativeId: 1,
  type: 1,
  effectiveDate: 1,
  value: 1,
  valueData: 1,
  assuranceStatus: 1,
  compositeData: 1,
  lastUpdated: 1,
  stakeholders: 1,
  valueType: 1,
  period: 1,
};

export type ConnectionUtrv = Pick<UniversalTrackerValuePlain,
  '_id' |
  'value' |
  'valueData' |
  'initiativeId' |
  'compositeData' |
  'effectiveDate' |
  'assuranceStatus' |
  'universalTrackerId' |
  'type' |
  'period'
>

export const connectionUtrvFields: KeysEnum<ConnectionUtrv> = {
  _id: 1,
  universalTrackerId: 1,
  type: 1,
  initiativeId: 1,
  effectiveDate: 1,
  value: 1,
  period: 1,
  valueData: 1,
  assuranceStatus: 1,
  compositeData: 1,
}

export const universalTrackerValuePlainFields: KeysEnum<UniversalTrackerValuePlain> = {
  _id: 1,
  created: 1,
  endDate: 1,
  escalationPolicy: 1,
  evidence: 1,
  evidenceData: 1,
  evidenceRequired: 1,
  noteRequired: 1,
  instructions: 1,
  note: 1,
  notes: 1,
  stakeholders: 1,
  status: 1,
  verificationRequired: 1,
  universalTrackerId: 1,
  universalTracker: 1,
  initiativeId: 1,
  sourceType: 1,
  sourceCode: 1,
  type: 1,
  unit: 1,
  numberScale: 1,
  period: 1,
  effectiveDate: 1,
  deletedDate: 1,
  history: 1,
  value: 1,
  valueData: 1,
  lastUpdated: 1,
  assuranceStatus: 1,
  compositeData: 1,
  valueType: 1,
  isPrivate: 1,
  sourceItems: 1,
  valueAggregation: 1,
  initiative: 1,
  survey: 1,
};

export const organizationPermissionsMinProjection: KeysEnum<OrganizationPermissionsMin> = {
  _id: 1,
  permissions: 1
};
