import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import BackgroundJob, {
  BackgroundJobModel,
  JobType,
  Task,
  TaskStatus,
  TaskType,
  CreateJob,
} from '../../../models/backgroundJob';
import { BackgroundBaseWorkflow, TaskResult } from '../../background-process/BackgroundBaseWorkflow';
import { ObjectId } from 'bson';
import DocumentModel, { DocumentOwnerType, DocumentPlain } from '../../../models/document';
import { createLogEntry } from '../../jobs';
import { generatedUUID } from '../../crypto/token';
import { getAIDocumentLibraryScanService, AIDocumentUtrMappingService } from './AIDocumentUtrMappingService';
import ContextError from '../../../error/ContextError';
import { BackgroundJobService, getBackgroundJobService } from '../../background-process/BackgroundJobService';

export interface AIDocumentUtrMappingJobModel extends BackgroundJobModel {
  initiativeId: ObjectId;
}

type FetchDocumentsTask = Task<{}, TaskType.FetchDocuments>;
type ProcessDocumentTask = Task<{ documentId: ObjectId }, TaskType.ProcessDocument>;
type AIDocumentUtrMappingTask = FetchDocumentsTask | ProcessDocumentTask;

export class AIDocumentUtrMappingWorkflow extends BackgroundBaseWorkflow<AIDocumentUtrMappingJobModel> {
  protected jobType = JobType.AIDocumentUtrMapping;

  constructor(
    private documentModel: typeof DocumentModel,
    private backgroundJobModel: typeof BackgroundJob,
    private bgJobService: BackgroundJobService,
    private aiDocumentUtrMappingService: AIDocumentUtrMappingService,
    protected logger: LoggerInterface
  ) {
    super();
  }

  public isAIDocumentUtrMapping(job: BackgroundJobModel): job is AIDocumentUtrMappingJobModel {
    return job.type === JobType.AIDocumentUtrMapping && job.initiativeId !== undefined;
  }

  public async upsertJob(initiativeId: ObjectId, userId: ObjectId): Promise<AIDocumentUtrMappingJobModel> {
    const existingJob = await this.findProcessingJob(initiativeId);
    if (existingJob) {
      this.logger.info(`${this.jobType} job already exists for initiativeId: ${initiativeId}`, {
        existingJobId: existingJob._id,
      });
      return existingJob;
    }

    this.logger.info(`Creating ${this.jobType} job for initiativeId: ${initiativeId}`);

    const createData: CreateJob = {
      type: this.jobType,
      name: `Mapping UTRs to documents - Initiative: ${initiativeId}`,
      initiativeId,
      userId,
      tasks: [
        {
          id: generatedUUID(),
          type: TaskType.FetchDocuments,
          name: 'Fetch documents',
          status: TaskStatus.Pending,
          data: {},
        } satisfies FetchDocumentsTask,
      ],
      logs: [createLogEntry(`Job created for initiative ${initiativeId}`)],
    };

    const job = (await this.backgroundJobModel.create(createData)) as AIDocumentUtrMappingJobModel;
    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          cause: e,
        })
      );
    });

    return job;
  }

  protected async processTask(
    job: AIDocumentUtrMappingJobModel,
    task: AIDocumentUtrMappingTask
  ): Promise<TaskResult<AIDocumentUtrMappingJobModel>> {
    switch (task.type) {
      case TaskType.FetchDocuments:
        return this.handleFetchDocumentsTask(job, task);
      case TaskType.ProcessDocument:
        return this.handleProcessDocumentTask(job, task);
      default:
        throw new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
          jobId: job._id,
        });
    }
  }

  private async handleFetchDocumentsTask(
    job: AIDocumentUtrMappingJobModel,
    task: Task
  ): Promise<TaskResult<AIDocumentUtrMappingJobModel>> {
    await this.startTask(job, task);

    const documents = await this.documentModel
      .find(
        {
          ownerId: job.initiativeId,
          ownerType: DocumentOwnerType.Initiative,
        },
        { _id: 1 }
      )
      .lean<Pick<DocumentPlain, '_id'>[]>()
      .exec();

    if (documents.length === 0) {
      job.logs.push(
        createLogEntry(`No documents found for initiative ${job.initiativeId}.`, { metadata: { taskId: task.id } })
      );

      return { job: await this.completeTask(job, task), executeNextTask: false };
    }

    documents.forEach(({ _id: documentId }) => {
      job.tasks.push({
        id: generatedUUID(),
        type: TaskType.ProcessDocument,
        name: `Process Document ${documentId}`,
        status: TaskStatus.Pending,
        data: { documentId },
      } satisfies ProcessDocumentTask);
    });

    job.logs.push(createLogEntry(`Created ${documents.length} processing tasks.`, { metadata: { taskId: task.id } }));

    return { job: await this.completeTask(job, task), executeNextTask: true };
  }

  private async handleProcessDocumentTask(
    job: AIDocumentUtrMappingJobModel,
    task: ProcessDocumentTask
  ): Promise<TaskResult<AIDocumentUtrMappingJobModel>> {
    await this.startTask(job, task);

    const document = await this.documentModel.findById(task.data.documentId).orFail().lean<DocumentPlain>().exec();

    await this.aiDocumentUtrMappingService.mapRelevantUtrsWithoutValidating(document);

    return { job: await this.completeTask(job, task), executeNextTask: true };
  }
}

let instance: AIDocumentUtrMappingWorkflow;
export const getAIDocumentLibraryScanWorkflow = () => {
  if (!instance) {
    instance = new AIDocumentUtrMappingWorkflow(
      DocumentModel,
      BackgroundJob,
      getBackgroundJobService(),
      getAIDocumentLibraryScanService(),
      wwgLogger
    );
  }
  return instance;
};
