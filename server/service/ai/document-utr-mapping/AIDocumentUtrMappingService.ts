import { AIModelType, getAIModelFactory } from '../AIModelFactory';
import { ChatGPT } from '../models/ChatGPT';
import { DocumentPlain, DocumentOwnerType } from '../../../models/document';
import {
  DocumentUtrMapping,
  DocumentUtrMappingPlain,
  RelevantUtr,
} from '../../../models/documentUniversalTrackerMapping';
import ContextError from '../../../error/ContextError';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { downloadFile } from '../../file/file';
import { getStorage } from '../../storage/fileStorage';
import { AppSetting, AppSettingKey, AppSettingPlain } from '../../../models/app-setting';
import UniversalTracker, { UniversalTrackerPlain } from '../../../models/universalTracker';
import { KeysEnum } from '../../../models/commonProperties';
import { z } from 'zod';
import { BlueprintRepository, getBlueprintRepository } from '../../../repository/BlueprintRepository';
import { activeBlueprints } from '../../../survey/blueprints';
import Initiative, { InitiativePlain, isOrganization } from '../../../models/initiative';
import { FeatureTag } from '@g17eco/core';
import { FileSupportAiModel } from '../models/FIleSupportAiModel';

const MIN_RELEVANCE_SCORE_THRESHOLD = 0.7;

const relevantUtrsSchema = z.object({
  result: z.array(
    z.object({
      code: z.string(),
      score: z.number().min(0).max(1),
    })
  ),
});

const UTRS_FILE_NAME = 'universalTrackers.json';

type UtrPromptData = Pick<
  UniversalTrackerPlain,
  'code' | 'name' | 'type' | 'alternatives' | 'instructions' | 'valueLabel'
>;

const UTR_PROMPT_DATA_PROJECTION: KeysEnum<UtrPromptData, 1> = {
  code: 1,
  name: 1,
  type: 1,
  alternatives: 1,
  instructions: 1,
  valueLabel: 1,
};

export class AIDocumentUtrMappingService {
  constructor(
    private aiModel: FileSupportAiModel,
    private documentUtrMappingModel: typeof DocumentUtrMapping,
    private appSettingModel: typeof AppSetting,
    private logger: LoggerInterface,
    private utrModel: typeof UniversalTracker,
    private blueprintRepo: BlueprintRepository,
    private initiativeModel: typeof Initiative,
    private fileStorage: ReturnType<typeof getStorage>,
    private fileSystem: Pick<typeof fs, 'createReadStream' | 'unlinkSync'>
  ) {}

  /**
   * Use AI to scan a document and return a list of relevant UTRs with scores.
   * @param document DocumentPlain
   */
  public async mapRelevantUtrs(document: DocumentPlain): Promise<RelevantUtr[]> {
    if (!(await this.canDocumentBeMapped(document))) {
      return [];
    }

    return this.mapRelevantUtrsWithoutValidating(document);
  }

  /**
   * Use only for background job workflow since it validates document in previous tasks.
   */
  public async mapRelevantUtrsWithoutValidating(document: DocumentPlain): Promise<RelevantUtr[]> {
    let uploadedDocumentId = '';
    try {
      const existingScan = await this.documentUtrMappingModel
        .findOne({ documentId: document._id })
        .lean<DocumentUtrMappingPlain>()
        .exec();
      if (existingScan) {
        return existingScan.utrs;
      }

      this.logger.info(`Start mapping relevant UTRs for document using AI: ${document._id}`);

      const appSettings = await this.appSettingModel
        .find({
          key: { $in: [AppSettingKey.OpenAIRelevantUTRsDocumentScanAssistantId, AppSettingKey.OpenAIUTRsFileId] },
        })
        .lean<AppSettingPlain[]>()
        .exec();

      const [assistantId, utrsFileId, justUploadedDocumentId] = await Promise.all([
        this.getOrCreateAssistantId({ appSettings }),
        this.getOrCreateUtrsFileId({ appSettings }),
        this.uploadDocument(document),
      ]);
      uploadedDocumentId = justUploadedDocumentId;

      const relevantUtrs = await this.askForRelevantUTRs({
        assistantId,
        utrsFileId,
        uploadedDocumentId,
      });

      this.logger.info('Saving relevant UTRs', { documentId: document._id, relevantUtrCount: relevantUtrs.length });
      await this.documentUtrMappingModel.create({
        documentId: document._id,
        initiativeId: document.ownerId,
        utrs: relevantUtrs,
      });

      this.logger.info(`AI scan successful for document ${document._id}`, {
        relevantUtrCount: relevantUtrs.length,
      });
      return relevantUtrs;
    } catch (error) {
      const contextError =
        error instanceof ContextError
          ? error
          : new ContextError(`AI scan failed for document ${document._id}`, {
              documentId: document._id,
              cause: error,
            });
      throw contextError;
    } finally {
      if (uploadedDocumentId) {
        this.logger.info('Deleting uploaded document', { documentId: document._id, uploadedDocumentId });
        await this.aiModel.deleteFile(uploadedDocumentId);
      }
    }
  }

  private async canDocumentBeMapped(document: DocumentPlain): Promise<boolean> {
    if (document.ownerType !== DocumentOwnerType.Initiative || !document.ownerId) {
      return false;
    }

    const initiative = await this.initiativeModel
      .findById(document.ownerId, { tags: 1 })
      .lean<Pick<InitiativePlain, 'tags'>>()
      .exec();
    if (!initiative || !isOrganization(initiative)) {
      return false;
    }

    return Boolean(initiative.tags?.includes(FeatureTag.AIAccessDocumentLibrary));
  }

  private async getOrCreateAssistantId({ appSettings }: { appSettings: AppSettingPlain[] }) {
    const existedAssistantId = appSettings.find(
      (s) => s.key === AppSettingKey.OpenAIRelevantUTRsDocumentScanAssistantId
    )?.value;
    if (existedAssistantId) {
      return existedAssistantId;
    }

    this.logger.info('Assistant not found. Creating Assistant...');

    const assistantInstructions = `
      You are an AI assistant that maps uploaded documents to sustainability metrics called UTRs (Universal Trackers). Later, another process will use this mapping to fill in those metric values based on the documents' content.

      You will be given two attachments:
      1. A JSON file named "${UTRS_FILE_NAME}", containing a list of UTR objects.
      2. A document file (PDF, Word, image, or plain text) that contains narrative, data, or evidence to analyze.

      Your task is to:
      - Analyze the document content.
      - Identify and return only the UTRs from "${UTRS_FILE_NAME}" that document can provides information to supply a answer for those UTRs.
      - Score each UTR based on how well the document addresses each UTR.
      - Only include UTRs with a relevance score of **${MIN_RELEVANCE_SCORE_THRESHOLD} or higher**.

      ### UTR Object Fields (from "${UTRS_FILE_NAME}"):

      Each UTR has the following structure:

      - **code** *(string)*: Unique code representing a metric. It can contains information about metric standard.
      - **name** *(string)*: A short name representing the question, more concise than 'valueLabel'.
      - **type** *(string)*: Represents a sustainability standard.
      - **alternatives** *(array of strings)*: Alternative phrasings or labels that express the same question.
      - **instructions** *(string)*: Data entry guidance — where to get the data, expected format, and any required supporting evidence.
      - **valueLabel** *(string)*: The full question label displayed to users when answering the metric.

      Use these fields to decide if the document contains the answer:
      - Check if the document text explicitly responds to the question implied by each UTR's **name**, **valueLabel**, **instructions** or **alternatives**.
      - If the document text provides a clear, direct answer (for example, it states “Our facility reduced water usage by 15%”), then that UTR is answerable by this document.
      - If the document only implies the concept without giving concrete data or phrasing that directly addresses the metric's question, do not include that UTR.

      ### Output Requirements:
      
      Return a list of matching UTRs in JSON format. Each returned UTR must include:
      - 'code': The UTR code.
      - 'score': A relevance score (between 0 and 1) indicating how well the UTR matches the document.

      ### Scoring Guidance:
      - **Score = 1.0** if the document contains a verbatim or highly specific answer to the metric (e.g., the exact numbers or terminology the UTR asks for).
      - **Score between 0.7-0.9** if the document's language clearly addresses the metric's topic but with slightly different wording (e.g., “we saved 15% water” versus “water usage reduction”).
      - **Omit any UTR** for which the document lacks sufficient information to answer the question.

      Exclude any commentary or explanation — return only the structured JSON result as specified.
      `.trim();

    const assistant = await this.aiModel.createAssistant({
      name: 'Relevant UTRs Document Mapper',
      instructions: assistantInstructions,
      tools: [{ type: 'code_interpreter' }],
    });
    const assistantId = assistant.id;

    this.logger.info('Assistant created.', { assistant });

    await this.appSettingModel.create({
      key: AppSettingKey.OpenAIRelevantUTRsDocumentScanAssistantId,
      value: assistantId,
    });

    return assistantId;
  }

  private async getOrCreateUtrsFileId({ appSettings }: { appSettings: AppSettingPlain[] }) {
    const existedUtrsFileId = appSettings.find((s) => s.key === AppSettingKey.OpenAIUTRsFileId)?.value;
    if (existedUtrsFileId) {
      return existedUtrsFileId;
    }

    this.logger.info('UTRs file not found. Creating UTRS file using blueprint...');
    const utrCodes = await this.blueprintRepo.getSurveyUtrCodes(activeBlueprints);
    const utrs = await this.utrModel
      .find({ code: { $in: utrCodes } }, UTR_PROMPT_DATA_PROJECTION)
      .lean<UtrPromptData[]>();

    const file = new File([JSON.stringify(utrs)], UTRS_FILE_NAME, { type: 'application/json' });
    const utrsFile = await this.aiModel.createFile({ file });
    const utrsFileId = utrsFile.id;

    this.logger.info('UTRs file created.', { utrsFile });

    await this.appSettingModel.create({ key: AppSettingKey.OpenAIUTRsFileId, value: utrsFileId });

    return utrsFileId;
  }

  private async uploadDocument(document: DocumentPlain) {
    this.logger.info('Uploading document', { documentId: document._id });

    const tempFilePath = path.join('/tmp', `${document._id}-${uuidv4()}.${document.metadata?.extension || ''}`);

    // Download the file
    const [signedUrl] = await this.fileStorage.getSignedUrl(document.path);
    await downloadFile(signedUrl, tempFilePath);

    const uploadedFile = await this.aiModel.createFile({
      file: this.fileSystem.createReadStream(tempFilePath),
      purpose: 'user_data',
    });
    this.logger.info('Document uploaded', { documentId: document._id, uploadedFile });

    this.fileSystem.unlinkSync(tempFilePath);

    return uploadedFile.id;
  }

  private async askForRelevantUTRs({
    assistantId,
    utrsFileId,
    uploadedDocumentId,
  }: {
    assistantId: string;
    utrsFileId: string;
    uploadedDocumentId: string;
  }) {
    this.logger.info('Asking AI for relevant UTRs...');

    const threadMessageContent = `
      The attached file with UTRs is a JSON file named "${UTRS_FILE_NAME}". The other file is the document to analyze.

      Please identify all relevant UTRs from "${UTRS_FILE_NAME}" based on the content of the uploaded document.

      Only return UTRs with a score of at least ${MIN_RELEVANCE_SCORE_THRESHOLD}.
      Return only structured data (no explanations or commentary).
      `.trim();
    return (
      (
        await this.aiModel.runThreadWithAssistant({
          assistantId,
          message: {
            role: 'user',
            content: threadMessageContent,
            attachments: [
              {
                file_id: utrsFileId,
                tools: [{ type: 'code_interpreter' }],
              },
              {
                file_id: uploadedDocumentId,
                tools: [{ type: 'code_interpreter' }],
              },
            ],
          },
          jsonSchema: relevantUtrsSchema,
        })
      )?.result || []
    );
  }
}

let instance: AIDocumentUtrMappingService | undefined;
export const getAIDocumentLibraryScanService = () => {
  if (!instance) {
    instance = new AIDocumentUtrMappingService(
      getAIModelFactory().getModel(AIModelType.ChatGPT) as ChatGPT,
      DocumentUtrMapping,
      AppSetting,
      wwgLogger,
      UniversalTracker,
      getBlueprintRepository(),
      Initiative,
      getStorage(),
      fs
    );
  }
  return instance;
};
