/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { <PERSON>gger<PERSON>nterface, wwgLogger } from '../../wwgLogger';
import BackgroundJob, { JobStatus, JobType, LogMessage, TaskStatus, TaskType } from '../../../models/backgroundJob';
import ContextError from '../../../error/ContextError';
import { BackgroundBaseWorkflow, TaskResult } from '../../background-process/BackgroundBaseWorkflow';
import {
  SupportedJobModel,
  WorkflowCreate,
  CreatedJob,
  NotificationParams,
  TaskAIAutoAnswerProcess,
  AIAutoAnswerTask,
  TaskAIAutoAnswerComplete,
  TaskAIAutoAnswerSetup,
  ProcessedContext,
  ReturnedJob,
} from './types';
import { createLogEntry } from '../../../service/jobs/logMessage';
import { ObjectId } from 'bson';
import { getNotificationService, NotificationService } from '../../../service/notification/NotificationService';
import {
  CustomAttributes,
  NotificationCategory,
  NotificationPage,
} from '../../../service/notification/NotificationTypes';
import { UrlMapper } from '../../../service/url/UrlMapper';
import { LEVEL } from '../../../service/event/Events';
import User from '../../../models/user';
import { tryGetContext } from '../../../middleware/audit/contextMiddleware';
import { getSurveyAutoAnswerService, SurveyAutoAnswerService } from './SurveyAutoAnswerService';
import { getRootInitiativeService, RootInitiativeService } from '../../../service/organization/RootInitiativeService';
import Initiative, { InitiativePlain } from '../../../models/initiative';
import { generatedUUID } from '../../../service/crypto/token';
import { getBackgroundJobService } from '../../../service/background-process/BackgroundJobService';

export class SurveyAutoAnswerWorkflow extends BackgroundBaseWorkflow<SupportedJobModel> {
  protected jobType = JobType.AIAutoAnswerSurvey;

  constructor(
    protected logger: LoggerInterface,
    private autoAnswerService: SurveyAutoAnswerService,
    private notificationService: NotificationService,
    private rootInitiativeService: RootInitiativeService
  ) {
    super();
  }

  public async findLatestJob(initiativeId: ObjectId, surveyId: string) {
    const idempotencyKey = this.getIdempotencyKey({
      id: initiativeId.toHexString(),
      idempotencyKey: `survey-${surveyId}`,
    });

    const job = await BackgroundJob.findOne(
      {
        initiativeId,
        type: this.jobType,
        idempotencyKey,
      },
      { _id: 1, status: 1, tasks: { id: 1, type: 1, status: 1 }, updated: 1 }
    )
      .sort({ created: -1 })
      .lean<ReturnedJob>()
      .exec();

    return { job };
  }

  public async createOrRetry(workflow: WorkflowCreate): Promise<CreatedJob> {
    const { initiativeId, surveyId, userId } = workflow;

    const idempotencyKey = this.getIdempotencyKey({
      id: initiativeId.toHexString(),
      idempotencyKey: `survey-${surveyId}`,
    });

    // Ensure we are not spamming the same job again and again.
    const exists = await BackgroundJob.findOne({
      type: this.jobType,
      initiativeId,
      idempotencyKey,
      status: { $in: [JobStatus.Pending, JobStatus.Processing] },
    });

    if (exists) {
      this.logger.error(
        new ContextError(`AI auto answer job already exists for this configuration`, {
          initiativeId,
          idempotencyKey,
          jobType: this.jobType,
          existingJobId: exists._id,
        })
      );
      // re-run the Job
      await getBackgroundJobService().runFromJob(exists);
      return {
        jobId: exists._id.toString(),
        status: exists.status,
      };
    }

    return this.autoAnswerService.createJob({ initiativeId, surveyId, userId, idempotencyKey });
  }

  private getProcessAnswerTask(context: { utrvId: ObjectId; surveyId: ObjectId }): TaskAIAutoAnswerProcess {
    const taskId = generatedUUID();
    return {
      id: taskId,
      name: `Answering utrv: ${context.utrvId} in survey: ${context.surveyId.toString()}`,
      type: TaskType.AIAutoAnswerProcess,
      status: TaskStatus.Pending,
      data: {
        utrvId: context.utrvId,
      },
    };
  }

  private async setupTask(job: SupportedJobModel, task: TaskAIAutoAnswerSetup) {
    await this.startTask(job, task);

    const { utrvIds, surveyId } = task.data;

    utrvIds.forEach((utrvId) => {
      const task = this.getProcessAnswerTask({ utrvId, surveyId });
      job.tasks.push(task);
    });

    const completeTask: TaskAIAutoAnswerComplete = this.getCompleteTask();
    job.tasks.push(completeTask);

    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.logs.push(
      createLogEntry(`Completed ${task.name} processing`, {
        metadata: {
          taskId: task.id,
        },
      })
    );

    job.markModified('tasks');
    return job.save();
  }

  private async processAnswerTask(job: SupportedJobModel, task: TaskAIAutoAnswerProcess) {
    await this.startTask(job, task);

    const initiative = await Initiative.findById(job.initiativeId).orFail().lean<InitiativePlain>().exec();
    const { utrvId } = task.data;

    const processedResult = await this.autoAnswerService.processAnswerUtrv({
      initiative,
      utrvId,
      userId: job.userId,
      jobId: job._id,
    });

    (task as TaskAIAutoAnswerProcess<ProcessedContext>).data = {
      utrvId,
      isSuccess: processedResult.isSuccess,
      ...(processedResult.errorMessage ? { errorMessage: processedResult.errorMessage } : {}),
    };
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    this.logger.info(`Completed answering utrvId: ${utrvId.toString()}`, processedResult);

    job.markModified('tasks');
    return job.save();
  }

  public getCompleteTask(): TaskAIAutoAnswerComplete {
    const taskId = generatedUUID();
    return {
      id: taskId,
      name: `Complete automatic answer the survey`,
      type: TaskType.AIAutoAnswerComplete,
      status: TaskStatus.Pending,
      data: {
        completedUtrvs: [],
        errorUtrvs: [],
      },
    };
  }

  private async completeTaskHandler(job: SupportedJobModel, task: TaskAIAutoAnswerComplete) {
    await this.startTask(job, task);

    const setupTask = job.tasks.find((t) => t.type === TaskType.AIAutoAnswerSetup);
    if (!setupTask) {
      throw new ContextError('Not found a set up task to process', { jobId: job._id, taskId: task.id });
    }
    const surveyId = setupTask.data.surveyId;

    const processTasks = job.tasks.filter(
      (task) => task.type === TaskType.AIAutoAnswerProcess
    ) as TaskAIAutoAnswerProcess<ProcessedContext>[];
    const { completedUtrvs, errorUtrvs } = processTasks.reduce(
      (acc, task) => {
        if (task.data.isSuccess) {
          acc.completedUtrvs.push(task.data.utrvId);
        } else {
          acc.errorUtrvs.push(task.data.utrvId);
        }
        return acc;
      },
      { completedUtrvs: [] as ObjectId[], errorUtrvs: [] as ObjectId[] }
    );

    task.data = {
      completedUtrvs,
      errorUtrvs,
    };
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    const logMessage = `Completed the process of automatic answer the survey`;
    job.logs.push(
      createLogEntry(logMessage, {
        metadata: {
          jobId: job._id.toString(),
          taskId: task.id,
          initiativeId: job.initiativeId.toString(),
        },
      })
    );

    const user = await User.findById(job.userId).orFail().lean().exec();

    try {
      const log = await this.sendNotification({
        title: logMessage,
        content: `Check out for updates.`,
        job,
        user,
        surveyId,
        initiativeId: job.initiativeId,
      });
      job.logs.push(log);
    } catch (e) {
      job.logs.push(
        createLogEntry(`Failed to AI automatic answer for survey notification`, {
          severity: LEVEL.ERROR,
          metadata: {
            userId: user._id.toString(),
            jobId: job._id.toString(),
            errorMessage: e.message,
          },
        })
      );
    }

    job.markModified('tasks');
    return job.save();
  }

  private async sendNotification({
    job,
    user,
    title,
    content,
    surveyId,
    initiativeId,
  }: NotificationParams): Promise<LogMessage> {
    const initiative = await Initiative.findById(initiativeId).orFail().lean().exec();
    const org = await this.rootInitiativeService.getOrganization(initiative);
    const jobId = job._id.toString();
    const surveyIdStr = surveyId.toString();

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      page: NotificationPage.SurveyOverview,
      domain: job.attributes.find((attr) => attr.name === 'domain')?.value || tryGetContext()?.origin,
      initiativeId: initiativeId.toString(),
      surveyId: surveyIdStr,
      jobId: jobId,
    };

    const notificationId = new ObjectId();
    await this.notificationService.createNotification({
      _id: notificationId,
      title,
      content,
      category: NotificationCategory.Announcements,
      topic: `AI-auto-answer-survey-${surveyIdStr}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients: [{ id: String(user._id) }],
    });

    return createLogEntry(`Finish AI automatic answer for surveyId: ${surveyIdStr}`, {
      metadata: {
        notificationId,
        userId: user._id.toString(),
        jobId: jobId,
      },
    });
  }

  public async processTask(job: SupportedJobModel, task: AIAutoAnswerTask): Promise<TaskResult<SupportedJobModel>> {
    switch (task.type) {
      case TaskType.AIAutoAnswerSetup:
        return {
          job: await this.setupTask(job, task),
          // Allow to go to next job straight away in the same process
          executeNextTask: true,
        };
      case TaskType.AIAutoAnswerProcess:
        return {
          job: await this.processAnswerTask(job, task),
          executeNextTask: true,
        };
      case TaskType.AIAutoAnswerComplete:
        return {
          job: await this.completeTaskHandler(job, task),
          executeNextTask: true,
        };
      default:
        throw new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
          jobId: job._id,
        });
    }
  }
}

let instance: SurveyAutoAnswerWorkflow;
export const getSurveyAutoAnswerWorkflow = () => {
  if (!instance) {
    instance = new SurveyAutoAnswerWorkflow(
      wwgLogger,
      getSurveyAutoAnswerService(),
      getNotificationService(),
      getRootInitiativeService()
    );
  }
  return instance;
};
