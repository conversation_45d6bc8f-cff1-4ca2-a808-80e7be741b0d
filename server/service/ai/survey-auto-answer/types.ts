import { HydratedDocument } from 'mongoose';
import { BackgroundJob<PERSON>lain, JobStatus, JobType, Task, TaskType } from '../../../models/backgroundJob';
import { ObjectId } from 'bson';
import { UserPlain } from '../../../models/user';
import { ValueData } from '../../../models/public/universalTrackerValueType';

type SetupContext = {
  utrvIds: ObjectId[];
  surveyId: ObjectId;
};
export interface TaskAIAutoAnswerSetup extends Task<SetupContext> {
  type: TaskType.AIAutoAnswerSetup;
}
type InitialProcessContext = {
  utrvId: ObjectId;
};
export type ProcessedContext = Pick<ProcessedAnswerResult, 'isSuccess' | 'errorMessage'> & {
  utrvId: ObjectId;
};
type ProcessContext = InitialProcessContext | ProcessedContext;
export interface TaskAIAutoAnswerProcess<T extends ProcessContext = InitialProcessContext> extends Task<T> {
  type: TaskType.AIAutoAnswerProcess;
}

type CompleteContext = {
  completedUtrvs: ObjectId[];
  errorUtrvs: ObjectId[];
};
export interface TaskAIAutoAnswerComplete extends Task<CompleteContext> {
  type: TaskType.AIAutoAnswerComplete;
}
export type AIAutoAnswerTask = TaskAIAutoAnswerSetup | TaskAIAutoAnswerProcess | TaskAIAutoAnswerComplete;
export type SupportedJobPlain = Omit<BackgroundJobPlain<AIAutoAnswerTask[]>, 'initiativeId' | 'userId'> & {
  type: JobType.AIAutoAnswerSurvey;
  initiativeId: ObjectId;
  userId: ObjectId;
};
export type SupportedJobModel = HydratedDocument<SupportedJobPlain>;

export interface WorkflowCreate {
  initiativeId: ObjectId;
  surveyId: ObjectId;
  userId: ObjectId;
}

export interface CreatedJob {
  jobId: string;
  status: JobStatus;
}

export interface ProcessedAnswerResult {
  isSuccess: boolean;
  errorMessage?: string;
  value?: number;
  valueData?: ValueData;
  note?: string;
}

export interface NotificationParams {
  job: SupportedJobModel;
  user: Pick<UserPlain, '_id' | 'email'>;
  title: string;
  content: string;
  surveyId: ObjectId;
  initiativeId: ObjectId;
}

export type ReturnedJob = Pick<SupportedJobPlain, '_id' | 'type' | 'status' | 'updated'> & {
  tasks: Pick<AIAutoAnswerTask, 'id' | 'type' | 'status'>[];
};
