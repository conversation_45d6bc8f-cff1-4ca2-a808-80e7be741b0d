/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { AppCode, AppConfig, OnboardingStep } from '../AppConfig';
import { ProductCodes } from '../../../models/customer';
import { PERMISSION_GROUPS } from '../../../models/initiative';
import config from '../../../config';
import { BrandingTemplate } from '../../organization/domainConfig';
import { FeatureTag, getFeatureDetails } from '@g17eco/core';

export const defaultMT: AppConfig = {
  code: AppCode.MaterialityTracker,
  productCode: ProductCodes.MaterialityTracker,
  validProductCodes: [ProductCodes.MaterialityTracker],
  permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_STARTER,
  name: 'Materiality Assessment',
  logo: `${config.assets.cdn}/apps/default/Materiality_Tracker_logo.svg`,
  rootAppPath: 'materiality-tracker',
  onboardingPath: 'materiality-tracker',

  settings: {
    overviewRecommendedAddons: [],
    settingsRecommendedAddons: [],
    baseFeatures: [getFeatureDetails(FeatureTag.UsersXS), getFeatureDetails(FeatureTag.Verification)],
    onboardingSteps: [
      OnboardingStep.Signup,
      OnboardingStep.VerifyEmail,
      OnboardingStep.EmailDomainCompanyOnboardOrNot,
      OnboardingStep.ReferralInfo,
      OnboardingStep.CompanyInfo,
      OnboardingStep.CompleteMAT,
    ],
  },
  // Setup whitelabel as well to ensure we can override domainConfig if selected
  whitelabel: {
    brandingTemplate: BrandingTemplate.G17Eco,
    emailLogo: config.assets.defaultLogo,
  },
};

export const matAppConfigs: AppConfig[] = [defaultMT];
