/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { ProductCodes } from '../../../models/customer';
import { UserAgreement } from '../../../models/user';
import { AppCode, AppConfig, defaultCTLFeatures, OnboardingStep, SurveyOverviewMode } from '../AppConfig';
import config from '../../../config';
import { BrandingTemplate, sgxLogo } from '../../organization/domainConfig';
import { PERMISSION_GROUPS } from '../../../models/initiative';
import { FeatureCode, FeatureTag, getFeatureDetails } from '@g17eco/core';

export const SGXESGenome: AppConfig = {
  code: AppCode.SGXESGenome,
  productCode: ProductCodes.SGXESGenome,
  validProductCodes: [ProductCodes.SGXESGenome],
  permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_LIGHT,

  name: 'SGX ESGenome',
  logo: `${config.assets.cdn}/apps/sgx/SGX-ESGenome.svg`,
  rootAppPath: 'sgx-esgenome',
  onboardingPath: 'sgx-esgenome',

  whitelabel: {
    logo: `${config.assets.cdn}/apps/sgx/sgx-first-logo.png`,
    brandingTemplate: BrandingTemplate.SGX,
    emailLogo: sgxLogo,
  },
  settings: {
    onboardingSteps: [
      OnboardingStep.SGXReferrerCodeIssuerLookup,
      OnboardingStep.IssuerCompanyOnboardOrNot,
      OnboardingStep.Signup,
      OnboardingStep.Activation,
      OnboardingStep.Complete
    ],
    defaultSurveyOverviewMode: SurveyOverviewMode.ScopeGroups,
    overviewRecommendedAddons: ['sgx_metrics', 'gri2021', 'issb', 'tcfd_standard', 'ctl', 'sgx_extended'],
    settingsRecommendedAddons: ['sgx_metrics', 'sgx_extended', 'tcfd_standard', 'issb'],
    userAgreementsRequired: [
      {
        code: UserAgreement.ESGenomeTandC
      }
    ],
    availableAddons: [
      'ctl',
      'sgx_metrics',
      'sgx_extended',
      'gri',
      'gri2021',
      'tcfd',
      'tcfd_standard',
      'ungc',
      'cdsb',
      'wef',
      'sasb',
      'cdp_2022',
      'rspo',
      'iogp',
      'ipieca',
      'asean_2024',
      'issb',
    ],
    baseFeatures: [
      ...defaultCTLFeatures,
      getFeatureDetails(FeatureTag.UsersL),
      {
        name: 'Custom Metrics - ESGenome (Limit 300)',
        code: FeatureCode.CustomMetrics,
        active: true,
        config: {
          limit: 300
        }
      },
    ],
  },
};
