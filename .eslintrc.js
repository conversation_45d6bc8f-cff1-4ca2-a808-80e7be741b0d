/*
Happy linting! 💖
*/
module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'unused-imports'],
  parserOptions: {
    project: ['./tsconfig.json'],
    tsconfigRootDir: __dirname,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended-type-checked',
    // Style related changes can be added at later date
    // 'plugin:@typescript-eslint/stylistic',
  ],
  rules: {
    '@typescript-eslint/no-explicit-any': 0,
    '@typescript-eslint/explicit-module-boundary-types': 0,
    '@typescript-eslint/no-unused-vars': 0,
    '@typescript-eslint/no-empty-function': 0,
    '@typescript-eslint/no-inferrable-types': 0,
    '@typescript-eslint/no-var-requires': 0,
    '@typescript-eslint/no-unsafe-return': 0,

    // Seems like there is no problem here at all. Very strict
    '@typescript-eslint/no-unsafe-enum-comparison': 0,
    '@typescript-eslint/require-await': 0,

    // Type related warnings and errors we don't want to deal with right now
    '@typescript-eslint/no-unsafe-member-access': 0,
    '@typescript-eslint/restrict-template-expressions': 0,
    '@typescript-eslint/consistent-type-definitions': 0,
    '@typescript-eslint/consistent-type-assertions': 0,
    '@typescript-eslint/consistent-indexed-object-style': 0,
    '@typescript-eslint/consistent-generic-constructor': 0,
    '@typescript-eslint/no-for-in-array': 0,

    // This seems to have bugs with declarations https://github.com/microsoft/TypeScript/issues/38347
    '@typescript-eslint/no-base-to-string': 0,

    // Should fix these as some point
    '@typescript-eslint/no-unsafe-assignment': 'warn',
    '@typescript-eslint/no-unsafe-argument': 'warn',
    '@typescript-eslint/no-unnecessary-type-assertion': 'warn',
    '@typescript-eslint/no-unsafe-call': 'warn',
    '@typescript-eslint/no-redundant-type-constituents': 'warn',
    '@typescript-eslint/restrict-plus-operands': 'warn',
    '@typescript-eslint/await-thenable': 'warn',
    '@typescript-eslint/unbound-method': 'warn',

    '@typescript-eslint/no-floating-promises': ['warn', { ignoreVoid: true }],

    '@typescript-eslint/no-misused-promises': [
      'error',
      {
        checksConditionals: true,
        checksVoidReturn: false,
      },
    ],
    'prefer-const': 'warn',
    '@typescript-eslint/ban-types': 0,
    'no-useless-escape': 0,
    'unused-imports/no-unused-imports': 'error',

    'max-params': 'off',
    '@typescript-eslint/max-params': ['warn', { max: 5 }],

    'no-restricted-syntax': [
      'warn',
      {
        selector: "NewExpression[callee.name='Error']",
        message: 'Use ContextError() or UserError() instead of Error().',
      },
    ],
    'no-restricted-imports': [
      'warn',
      {
        paths: [
          {
            name: 'moment',
            message:
              'Direct usage of "moment" is deprecated. Please use the utility functions in `server/util/date.ts` instead.',
          },
          {
            name: 'dayjs',
            message:
              'Direct usage of "dayjs" is deprecated. Please use the utility functions in `server/util/date.ts` instead.',
          },
        ],
      },
    ],
    '@typescript-eslint/no-var-requires': 'warn',
  },
  overrides: [
    {
      files: ['*.js'],
      extends: ['plugin:@typescript-eslint/disable-type-checked'],
    },
    // To many warnings there to deal with, will need to gradually fix those
    {
      files: ['*.spec.ts', 'tests/**/*'],
      extends: ['plugin:@typescript-eslint/disable-type-checked'],
    },
  ],
  env: {
    es6: true,
    node: true,
  },
  ignorePatterns: ['node_modules/', 'dist/', 'coverage/', 'migrations/'],
};
