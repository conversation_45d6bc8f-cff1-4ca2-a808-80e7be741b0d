/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import {
  getBackwardWeekDates,
  getUTCEndOf,
  revertFormattedToDate,
  DateFormat,
  customDateFormat,
  isBefore,
  isAfter,
  getDiffAgo,
  hasChange,
  getMonthFromAbbreviation,
  getCurrentDateStr,
  getFinancialMonths,
  getLastNumberOfMonths,
} from '../../server/util/date';
import { expect } from 'chai';
import * as sinon from 'sinon';

describe.only('Date Util', function () {
  let clock: sinon.SinonFakeTimers;

  // Set up a fixed date for all tests - January 15, 2023 at 12:00:00 UTC
  const FIXED_DATE = new Date('2023-01-15T12:00:00.000Z');

  beforeEach(function () {
    // Mock the system clock to return our fixed date
    clock = sinon.useFakeTimers(FIXED_DATE.getTime());
  });

  afterEach(function () {
    // Restore the real clock after each test
    clock.restore();
  });
  describe('customDateFormat', () => {
    it('should format date', () => {
      const date = new Date('2024-01-01T00:00:00.000Z');
      const formatted = customDateFormat(date, DateFormat.MonthYear);
      expect(formatted).to.be.eq('January 2024');
    });
    it('should format date with utc', () => {
      const date = new Date('2024-01-01T00:00:00.000Z');
      const formatted = customDateFormat(date, DateFormat.MonthYear, false);
      expect(formatted).to.be.eq('January 2024');
    });
  });

  describe('getBackwardWeekDates', function () {
    it('should get all the dates between', function () {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');

      const dates = getBackwardWeekDates(a, b);
      expect(dates).to.deep.eq([
        new Date('2025-06-28T23:59:59.999Z'),
        new Date('2025-06-21T23:59:59.999Z'),
        new Date('2025-06-14T23:59:59.999Z'),
      ]);
    });
  });

  describe('isBefore', () => {
    it('should return true if before', () => {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');
      expect(isBefore(a, b)).to.be.true;
    });
    it('should return false if after', () => {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');
      expect(isBefore(b, a)).to.be.false;
    });
  });

  describe('isAfter', () => {
    it('should return true if after', () => {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');
      expect(isAfter(b, )).to.be.true;
    });
    it('should return false if before', () => {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');
      expect(isAfter(b, a)).to.be.false;
    });
  });

  describe('getUTCEndOf', () => {
    it('should get end the end of the year', () => {
      const date = getUTCEndOf('year', '2022-12-15T00:00:00.000Z');
      expect(date.toISOString()).to.be.eq('2022-12-31T23:59:59.999Z');
    });
  });

  describe('getDiffAgo', () => {
    it('should get diff in months', () => {
      // Date that is exactly 1 month before our fixed date (2023-01-15)
      const date = new Date('2022-12-15T12:00:00.000Z');
      const diff = getDiffAgo(date);
      expect(diff).to.be.eq('1mo ago');
    });
    it('should get diff in days', () => {
      // Date that is 30 days before our fixed date (2023-01-15)
      const date = new Date('2022-12-16T12:00:00.000Z');
      const diff = getDiffAgo(date);
      expect(diff).to.be.eq('30d ago');
    });
  });

  describe('hasChange', () => {
    it('should return true if different', () => {
      const date = new Date('2022-12-15T00:00:00.000Z');
      const diff = hasChange(date, new Date('2022-12-16T00:00:00.000Z'));
      expect(diff).to.be.true;
    });
    it('should return false if same', () => {
      const date = new Date('2022-12-15T00:00:00.000Z');
      const diff = hasChange(date, date);
      expect(diff).to.be.false;
    });
  });

  describe('getMonthFromAbbreviation', () => {
    it('should return full month name', () => {
      const month = getMonthFromAbbreviation('Jan');
      expect(month).to.be.eq('January');
    });
    it('should return undefined if not found', () => {
      const month = getMonthFromAbbreviation('Foo');
      expect(month).to.be.undefined;
    });
  });

  describe('getCurrentDateStr', () => {
    it('should return current date as string with default format', () => {
      const dateStr = getCurrentDateStr();
      expect(dateStr).to.be.eq('2023-01-15');
    });
    it('should return current date as string with custom format', () => {
      const dateStr = getCurrentDateStr(DateFormat.MonthYear);
      expect(dateStr).to.be.eq('January 2023');
    });
  });

  describe('getFinancialMonths', () => {
    it('should return all financial months', () => {
      const months = getFinancialMonths();
      expect(months).to.deep.eq([
        '',
        'january',
        'february',
        'march',
        'april',
        'may',
        'june',
        'july',
        'august',
        'september',
        'october',
        'november',
        'december',
      ]);
    });
  });

  describe('getLastNumberOfMonths', () => {
    it('should return last 4 months by default', () => {
      // From our fixed date of 2023-01-15, the last 4 months should be:
      // December 2022, November 2022, October 2022, September 2022
      const months = getLastNumberOfMonths();
      expect(months).to.deep.eq(['december', 'november', 'october', 'september']);
    });
    it('should return specified number of months', () => {
      // From our fixed date of 2023-01-15, the last 2 months should be:
      // December 2022, November 2022
      const months = getLastNumberOfMonths(2);
      expect(months).to.deep.eq(['december', 'november']);
    });
  });

  describe('revertFormattedToDate', () => {
    describe('should revert formatted date to date', () => {
      it(DateFormat.MonthYear, () => {
        const date = revertFormattedToDate('January 2024', DateFormat.MonthYear);
        expect(date.getTime()).to.be.eq(new Date('2024-01-01T00:00:00.000Z').getTime());
      });

      it(DateFormat.SortableSlash, () => {
        const date = revertFormattedToDate('20/11/2023 09:28:00', DateFormat.SortableSlash);
        expect(date.getTime()).to.be.eq(new Date('2023-11-20T09:28:00.000Z').getTime());
      });
    });
  });
});
