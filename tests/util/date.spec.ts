/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import {
  getBackwardWeekDates,
  getUTCEndOf,
  revertFormattedToDate,
  DateFormat,
  customDateFormat,
  isBefore,
  isAfter,
  getDiffAgo,
  hasChange,
  getMonthFromAbbreviation,
  getCurrentDateStr,
  getFinancialMonths,
  getLastNumberOfMonths,
} from '../../server/util/date';
import { expect } from 'chai';

describe('Date Util', function () {
  describe('customDateFormat', () => {
    it('should format date', () => {
      const date = new Date('2024-01-01T00:00:00.000Z');
      const formatted = customDateFormat(date, DateFormat.MonthYear);
      expect(formatted).to.be.eq('January 2024');
    });
    it('should format date with utc', () => {
      const date = new Date('2024-01-01T00:00:00.000Z');
      const formatted = customDateFormat(date, DateFormat.MonthYear, false);
      expect(formatted).to.be.eq('January 2024');
    });
  });

  describe('getBackwardWeekDates', function () {
    it('should get all the dates between', function () {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');

      const dates = getBackwardWeekDates(a, b);
      expect(dates).to.deep.eq([
        new Date('2025-06-28T23:59:59.999Z'),
        new Date('2025-06-21T23:59:59.999Z'),
        new Date('2025-06-14T23:59:59.999Z'),
      ]);
    });
  });

  describe('isBefore', () => {
    it('should return true if before', () => {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');
      expect(isBefore(a, b)).to.be.true;
    });
    it('should return false if after', () => {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');
      expect(isBefore(b, a)).to.be.false;
    });
  });

  describe('isAfter', () => {
    it('should return true if after', () => {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');
      expect(isAfter(a, b)).to.be.true;
    });
    it('should return false if before', () => {
      const a = new Date('2025-06-28T23:59:59.999Z');
      const b = new Date('2025-06-08T00:00:00.000Z');
      expect(isAfter(b, a)).to.be.false;
    });
  });

  describe('getUTCEndOf', () => {
    it('should get end the end of the year', () => {
      const date = getUTCEndOf('year', '2022-12-15T00:00:00.000Z');
      expect(date.toISOString()).to.be.eq('2022-12-31T23:59:59.999Z');
    });
  });

  describe('getDiffAgo', () => {
    it('should get diff in months', () => {
      const date = new Date('2022-12-15T00:00:00.000Z');
      const diff = getDiffAgo(date);
      expect(diff).to.be.eq('1mo ago');
    });
    it('should get diff in days', () => {
      const date = new Date('2022-12-15T00:00:00.000Z');
      const diff = getDiffAgo(date);
      expect(diff).to.be.eq('30d ago');
    });
  });

  describe('hasChange', () => {
    it('should return true if different', () => {
      const date = new Date('2022-12-15T00:00:00.000Z');
      const diff = hasChange(date, new Date('2022-12-16T00:00:00.000Z'));
      expect(diff).to.be.true;
    });
    it('should return false if same', () => {
      const date = new Date('2022-12-15T00:00:00.000Z');
      const diff = hasChange(date, date);
      expect(diff).to.be.false;
    });
  });

  describe('getMonthFromAbbreviation', () => {
    it('should return full month name', () => {
      const month = getMonthFromAbbreviation('Jan');
      expect(month).to.be.eq('January');
    });
    it('should return undefined if not found', () => {
      const month = getMonthFromAbbreviation('Foo');
      expect(month).to.be.undefined;
    });
  });

  describe('getFinancialMonths', () => {
    it('should return all financial months', () => {
      const months = getFinancialMonths();
      expect(months).to.deep.eq([
        '',
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ]);
    });
  });

  describe('getLastNumberOfMonths', () => {
    it('should return last 4 months', () => {
      const months = getLastNumberOfMonths();
      expect(months).to.deep.eq(['january', 'december', 'november', 'october']);
    });
  });

  describe('revertFormattedToDate', () => {
    describe('should revert formatted date to date', () => {
      it(DateFormat.MonthYear, () => {
        const date = revertFormattedToDate('January 2024', DateFormat.MonthYear);
        expect(date.getTime()).to.be.eq(new Date('2024-01-01T00:00:00.000Z').getTime());
      });

      it(DateFormat.SortableSlash, () => {
        const date = revertFormattedToDate('20/11/2023 09:28:00', DateFormat.SortableSlash);
        expect(date.getTime()).to.be.eq(new Date('2023-11-20T09:28:00.000Z').getTime());
      });
    });
  });
});
