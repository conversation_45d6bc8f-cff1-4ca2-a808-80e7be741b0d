import sinon from 'sinon';
import { DashboardTemplatesService } from '../../../server/service/insight-dashboard/DashboardTemplatesService';
import { expect } from 'chai';
import { ObjectId } from 'bson';
import { getGreenProjectService } from '../../../server/service/integration/green-project/GreenProjectService';
import { getGroup } from '@g17eco/core';
import { DashboardTemplateType, templateDashboard } from '../../../server/service/insight-dashboard/template';
import { createSimpleDashboard } from '../../../tests/fixtures/dashboardFixtures';
import { initiativeOneSimple } from '../../fixtures/initiativeFixtures';
import { InsightDashboardType } from '../../../server/models/insightDashboard';
import { universalTrackerOne } from '../../fixtures/universalTrackerFixtures';
import { utrvOne } from '../../fixtures/universalTrackerValueFixtures';
import { UtrValueType } from '../../../server/models/public/universalTrackerType';

describe('DashboardTemplatesService', () => {
  let dashboardTemplatesService: DashboardTemplatesService;
  let surveyScopeService: any;
  let integrationManager: any;
  let insightDashboardModel: any;
  let dashboardItemManager: any;

  const TEMPLATES = [
    {
      value: 'wfn',
      label: 'Workforce Nutrition',
      icon: getGroup('standards', 'wfn_2024')?.src,
    },
    {
      value: 'gpt',
      label: 'Emissions Dashboard',
      icon: getGreenProjectService().getInfo().logo,
    },
  ];

  beforeEach(() => {
    surveyScopeService = {
      getUsedScopes: sinon.stub(),
    };

    integrationManager = {
      getActiveServices: sinon.stub(),
    };

    insightDashboardModel = {
      create: sinon.stub(),
    };

    dashboardItemManager = {
      populateData: sinon.stub(),
    };

    dashboardTemplatesService = new DashboardTemplatesService(
      surveyScopeService,
      integrationManager,
      insightDashboardModel,
      dashboardItemManager
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getTemplates', () => {
    it('should return GPT template if initiative has GPT integration active', async () => {
      integrationManager.getActiveServices.resolves([{ code: 'green-project-tech' }]);
      surveyScopeService.getUsedScopes.resolves({ standards: {}, frameworks: {} });

      const result = await dashboardTemplatesService.getTemplates({ initiativeId: new ObjectId() });

      expect(result).to.deep.equal([TEMPLATES[1]]);
    });

    it('should return WFN template if initiative has WFN module', async () => {
      integrationManager.getActiveServices.resolves([]);
      surveyScopeService.getUsedScopes.resolves({ standards: { wfn_2024: [] }, frameworks: {} });

      const result = await dashboardTemplatesService.getTemplates({ initiativeId: new ObjectId() });

      expect(result).to.deep.equal([TEMPLATES[0]]);
    });

    it('should return both templates if initiative has GPT integration active and WFN module', async () => {
      integrationManager.getActiveServices.resolves([{ code: 'green-project-tech' }]);
      surveyScopeService.getUsedScopes.resolves({ standards: { wfn_2024: [] }, frameworks: {} });

      const result = await dashboardTemplatesService.getTemplates({ initiativeId: new ObjectId() });

      expect(result).to.deep.equal(TEMPLATES);
    });
  });

  describe('createDashboard', () => {
    it('should throw error if initiative does not support the template', async () => {
      integrationManager.getActiveServices.resolves([]);
      surveyScopeService.getUsedScopes.resolves({ standards: {}, frameworks: {} });

      await expect(
        dashboardTemplatesService.createDashboard({
          initiativeId: new ObjectId(),
          creatorId: new ObjectId(),
          templateType: DashboardTemplateType.WFN,
        })
      ).to.be.rejectedWith('Template not support for this initiative');
    });

    it('should create WFN dashboard', async () => {
      surveyScopeService.getUsedScopes.resolves({ standards: { wfn_2024: [] }, frameworks: {} });
      insightDashboardModel.create.resolves({});

      const initiativeId = new ObjectId();
      const creatorId = new ObjectId();
      const templateType = DashboardTemplateType.WFN;
      await dashboardTemplatesService.createDashboard({
        initiativeId,
        creatorId,
        templateType,
      });

      expect(insightDashboardModel.create.firstCall.args[0]).to.deep.equals({
        ...templateDashboard[templateType],
        initiativeId,
        creatorId,
      });
    });

    it('should create GPT dashboard', async () => {
      integrationManager.getActiveServices.resolves([{ code: 'green-project-tech' }]);
      surveyScopeService.getUsedScopes.resolves({ standards: {}, frameworks: {} });
      insightDashboardModel.create.resolves({});

      const initiativeId = new ObjectId();
      const creatorId = new ObjectId();
      const templateType = DashboardTemplateType.GPT;
      await dashboardTemplatesService.createDashboard({
        initiativeId,
        creatorId,
        templateType,
      });

      expect(insightDashboardModel.create.firstCall.args[0]).to.deep.equals({
        ...templateDashboard[templateType],
        initiativeId,
        creatorId,
      });
    });
  });

  describe('populateDashboardData', () => {
    const initiative = initiativeOneSimple;
    const filters = {};
    const dashboard = createSimpleDashboard({ type: InsightDashboardType.Custom });
    const utr = { ...universalTrackerOne, valueType: UtrValueType.ValueList };
    const utrv = {
      ...utrvOne,
      universalTrackerId: universalTrackerOne._id,
      valueData: { data: 'wfn/2024/1-1/option1' },
    };
    const utrsData = [
      {
        utr,
        utrvs: [utrv],
      },
    ];
    const populateData = { ...dashboard, utrsData };

    it('should populate normal dashboard data if template is not WFN', async () => {
      dashboardItemManager.populateData.resolves(populateData);

      const result = await dashboardTemplatesService.populateDashboardData({ dashboard, filters, initiative });

      expect(result).to.deep.equal(populateData);
    });

    it('should populate WFN dashboard data if template is WFN', async () => {
      dashboardItemManager.populateData.resolves(populateData);

      const result = await dashboardTemplatesService.populateDashboardData({
        dashboard: { ...dashboard, type: InsightDashboardType.WFNTemplate },
        filters,
        initiative,
      });

      expect(result).to.deep.equal({
        ...populateData,
        utrsData: [
          {
            utr,
            utrvs: [{ ...utrv, valueData: { data: 1 } }],
          },
        ],
      });
    });
  });
});
