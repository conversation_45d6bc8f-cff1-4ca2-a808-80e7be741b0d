/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ObjectId } from "bson";
import {
  EntryEmissions,
  GreenlyCompanyMinimal,
} from "../../../../server/service/integration/greenly/greenlyTypes";
import IntegrationConnection from "../../../../server/models/integrationConnection";

const getGreenlyConnection = (initiativeId = new ObjectId()) => {
  const connection = new IntegrationConnection({
    initiativeId,
    integrationCode: 'greenly',
    status: 'pending',
    data: {
      answers: [
        {
          utrCode: 'greenly-registration-details',
          valueData: {
            table: [
              [
                { code: 'greenly-employees', value: 100 },
                { code: 'greenly-data-frequency', value: 'Quarterly' },
                { code: 'greenly-revenue', value: 1000000, unit: 'USD' }
              ]
            ]
          }
        }
      ],
      address: {
        line1: '123 Main St',
        city: 'Test City',
        country: 'Test Country',
        postcode: '12345'
      }
    },
    createdBy: new ObjectId()
  });

  // Mock mongoose methods
  connection.save = async () => connection;
  connection.markModified = () => {};

  return connection;
};

const createEmissionData = (): EntryEmissions[] => {
  return [
    {
      category: 'scope1',
      emissionsInKgCO2: 100,
      description: {
        en: 'Scope 1 direct emissions'
      },
      kgCO2: 100,
      kgCH4fossilAsCO2e: 0,
      kgCH4biogenicAsCO2e: 0,
      kgN2OAsCO2e: 0,
      kgOtherAsCO2e: 0,
      kgCO2Biogenic: 0,
      frenchOfficialCategory: 1,
      scope: 1
    },
    {
      category: 'scope2',
      emissionsInKgCO2: 200,
      description: {
        en: 'Scope 2 electricity emissions'
      },
      kgCO2: 200,
      kgCH4fossilAsCO2e: 0,
      kgCH4biogenicAsCO2e: 0,
      kgN2OAsCO2e: 0,
      kgOtherAsCO2e: 0,
      kgCO2Biogenic: 0,
      frenchOfficialCategory: 2,
      scope: 2
    },
    {
      category: 'scope3',
      emissionsInKgCO2: 300,
      description: {
        en: 'Scope 3 purchased goods emissions'
      },
      kgCO2: 300,
      kgCH4fossilAsCO2e: 0,
      kgCH4biogenicAsCO2e: 0,
      kgN2OAsCO2e: 0,
      kgOtherAsCO2e: 0,
      kgCO2Biogenic: 0,
      frenchOfficialCategory: 3,
      scope: 3
    }
  ];
};

export function createGreenlyFixtures() {
  const greenlyCompanyListItem: GreenlyCompanyMinimal = {
    id: 'company-123',
    name: 'Test Company',
    identifier: {
      type: 'operatorId',
      value: new ObjectId().toString(),
    }
  };


  return {
    greenlyCompanyListItem,
    getGreenlyConnection,
    createEmissionData
  };
}

