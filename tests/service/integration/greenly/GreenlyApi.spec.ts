/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import '../../../setup';
import axios from 'axios';
import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { getGreenlyApi, GreenlyApi } from '../../../../server/service/integration/greenly/GreenlyApi';
import { createGreenlyFixtures } from './GreenlyApiFixtures';
import ContextError from '../../../../server/error/ContextError';
import { GreenlyCompany, GreenlyCreateData } from '../../../../server/service/integration/greenly/greenlyTypes';
import { testLogger } from '../../../factories/logger';
import { userOne } from '../../../fixtures/userFixtures';

const { greenlyCompanyListItem, createEmissionData } = createGreenlyFixtures();

describe('GreenlyApi', () => {
  const sandbox = createSandbox();
  const httpClient = axios.create();
  const api = new GreenlyApi(httpClient, testLogger);

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    expect(getGreenlyApi()).to.be.instanceOf(GreenlyApi);
  });

  describe('listCompanies', () => {
    it('should return companies list', async () => {
      const mockResponse = {
        data: {
          companies: [greenlyCompanyListItem]
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.listCompanies();
      expect(result).to.deep.equal([greenlyCompanyListItem]);
    });

    it('should handle API error', async () => {
      const error = new ContextError('API Error');
      sandbox.stub(httpClient, 'get').rejects(error);

      await expect(api.listCompanies()).to.be.rejectedWith(error);
    });
  });

  describe('getCompany', () => {
    it('should return company by id', async () => {
      const mockResponse = {
        data: {
          companies: [greenlyCompanyListItem]
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.getCompany(greenlyCompanyListItem.id);
      expect(result).to.deep.equal(greenlyCompanyListItem);
    });

    it('should throw error when company not found', async () => {
      const mockResponse = {
        data: {
          companies: []
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);
      await expect(api.getCompany('non-existent-id')).to.be.rejectedWith(ContextError);
    });
  });

  describe('createConnection', () => {

    const createIdentifier = (id: string) => ({ type: 'operatorId', value: id });

    it('should create root company', async () => {
      const rootInitiative = {
        initiativeId: '123',
        name: 'Test Company',
        logo: 'logo.png',
        description: 'Test Description',
        industryLevel3: 'Test Industry',
        language: 'en',
        year: '2024',
        validityStatus: 'DEMO',
        type: 'standalone',
        countryCode: 'GB',
        parentInitiativeId: undefined,
      } satisfies GreenlyCreateData['rootInitiative'];
      const createData: GreenlyCreateData = {
        rootInitiative,
        user: userOne,
        additionalContext: [
          {
            name: 'Metric 1',
            value: '100',
            unit: 'kg',
            code: 'metric-1'
          }
        ],
        initiativeTree: [rootInitiative],
      };

      const fakeId = '**********-123123';

      sandbox.stub(httpClient, 'post').callsFake((url, data) => {
        if (url.includes('/users')) {
          return Promise.resolve({
            data: [{ id: 'user-123', email: '<EMAIL>' }]
          });
        }

        if (url.includes('/companies')) {
          const [first] = Array.isArray(data) ? data: [];
          return Promise.resolve({
            data: first ? [{ id: fakeId, name: first.name, identifier: first.identifier } as GreenlyCompany ] : []
          });
        }
        return Promise.resolve({ data: [] });
      });
      sandbox.stub(httpClient, 'get').resolves({ data: { companies: [] } });

      const result = await api.createConnection(createData);

      expect(result.greenlyCompany.id).to.equal(fakeId);
      expect(result.greenlyCompany.identifier).to.deep.equal(createIdentifier(rootInitiative.initiativeId));
      expect(result.childrenCompanies).to.deep.equal([]);

    });

  });

  describe('getEmissionsDataTotal', () => {
    it('should return emissions data', async () => {
      const mockResponse = {
        data: createEmissionData()
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.getEmissionsDataTotal('company-123', 2024);
      expect(result).to.deep.equal(mockResponse.data);
    });

    it('should handle API error with context', async () => {
      const error = {
        message: 'API Error',
        response: {
          data: { error: 'Invalid request' }
        }
      };
      sandbox.stub(httpClient, 'get').rejects(error);
      await expect(api.getEmissionsDataTotal('company-123', 2024)).to.be.rejectedWith(ContextError);
    });
  });
});
