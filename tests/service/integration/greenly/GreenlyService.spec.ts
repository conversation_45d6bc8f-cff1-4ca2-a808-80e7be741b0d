/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import '../../../setup';
import {
  getGreenlyService,
  GreenlyService
} from "../../../../server/service/integration/greenly/GreenlyService";
import { expect } from "chai";
import { getGreenly<PERSON><PERSON> } from "../../../../server/service/integration/greenly/GreenlyApi";
import { testLogger } from "../../../factories/logger";
import { createSandbox } from "sinon";
import { initiativeOneSimple } from "../../../fixtures/initiativeFixtures";
import { userOne } from "../../../fixtures/userFixtures";
import { getIntegrationRepository } from "../../../../server/service/integration/IntegrationRepository";
import { ObjectId } from "bson";
import ContextError from "../../../../server/error/ContextError";
import { IntegrationConnectionPlain } from "../../../../server/models/integrationConnection";
import {
  GreenlyCompany,
} from "../../../../server/service/integration/greenly/greenlyTypes";
import {
  getIntegrationNotificationService
} from "../../../../server/service/integration/IntegrationNotificationService";
import { getSingleSignOnManager } from "../../../../server/service/integration/SingleSignOnManager";
import { createGreenlyFixtures } from "./GreenlyApiFixtures";

// Add GreenlyApiFixtures.ts after this test is reviewed and approved
const { getGreenlyConnection, greenlyCompanyListItem, createEmissionData } = createGreenlyFixtures();

describe('GreenlyService', () => {

  const greenlyApi = getGreenlyApi();
  const repo = getIntegrationRepository();
  const integrationNotificationService = getIntegrationNotificationService();

  // OktaManager Fails without valid token on init... must mock it
  getSingleSignOnManager({
    getUser: () => Promise.resolve({
      addToGroup: () => Promise.resolve()
    })
  } as any)

  const greenlyService = new GreenlyService(
    testLogger,
    greenlyApi,
    repo,
    integrationNotificationService,
    {
      addUsersToGroup: async () => [
        { status: 'success', userId: new ObjectId(), groupId: '123123' },
        { status: 'error', userId: new ObjectId(), groupId: '123123' },
      ]
    },
  );

  const allUtrCodes = greenlyService.getAvailableQuestions().then(utrs => utrs.map(utr => utr.code));

  const sandbox = createSandbox();

  it('should create instance', () => {
    const instance = getGreenlyService();
    expect(instance).to.be.instanceOf(GreenlyService);
  });

  describe('getSetupConfig fn', () => {
    it('should getProvider info', async () => {
      const instance = getGreenlyService();
      const result = await instance.getSetupConfig();
      expect(result).to.have.keys('provider', 'integration');
    });
  });

  describe('execute checks', () => {
    beforeEach(() => {
      sandbox.stub(integrationNotificationService, 'sendIntegrationActive').resolves();
    })

    afterEach(() => {
      sandbox.restore();
    });

    it('should call api list', () => {
      const listCompaniesStub = sandbox.stub(greenlyApi, 'listCompanies').resolves([]);
      greenlyService.executeChecks();
      expect(listCompaniesStub.calledOnce).eq(true);
    });

    it('should update status to matched version', async () => {
      const initiativeId = new ObjectId(greenlyCompanyListItem.identifier?.value || '');
      const connModel = getGreenlyConnection(initiativeId);
      const listCompaniesStub = sandbox.stub(greenlyApi, 'listCompanies').resolves([greenlyCompanyListItem] as GreenlyCompany[]);

      sandbox.stub(repo, 'getConnectionsByProvider').resolves([connModel]);
      const result = await greenlyService.executeChecks();
      expect(listCompaniesStub.calledOnce).eq(true);
      expect(result).deep.eq({ connectionIds: [connModel._id] });
      expect(connModel.status).eq('active');
      expect((connModel.data as { externalId: string }).externalId).eq(greenlyCompanyListItem.id);
    });
  });

  describe('getAvailableQuestions', () => {
    it('should return questions', async () => {
      const questions = await greenlyService.getAvailableQuestions();
      expect(questions).to.be.an('array').that.is.not.empty;
      questions.forEach(q => {
        expect(q).to.have.property('code');
        expect(q).to.have.property('valueType');
      });
    });
  });

  describe('create setup', () => {
    it('should setup Greenly', async () => {
      const apiStub = sandbox.stub(greenlyApi, 'createConnection').resolves({
        greenlyCompany: { id: '123123' } as GreenlyCompany,
        childrenCompanies: [],
      });

      const profile = 'https://example.com/test-logo.png'

      const { success, data } = await greenlyService.createSetup({
        integrationConnection: getGreenlyConnection(initiativeOneSimple._id),
        rootInitiative: { ...initiativeOneSimple, profile },
        user: userOne,
        address: { line1: 'street', postcode: 'AA1 2BB' },
        generatedAnswers: [],
        providerCode: 'greenly'
      });

      expect(apiStub.calledOnce).eq(true);

      const { rootInitiative: company } = apiStub.getCall(0).args[0];
      expect(company.logo).eq(profile);

      expect(success).eq(true);
      expect(data).to.have.keys('greenlyCompany', 'childrenCompanies');
    });
  });

  describe('generateIntegrationData', () => {
    const emissionData = createEmissionData();

    const greenlyCompany = {
      ...greenlyCompanyListItem,
    } as GreenlyCompany;

    afterEach(() => {
      sandbox.restore();
    })

    async function getDataLookup(connection: IntegrationConnectionPlain) {
      const codes = await allUtrCodes;
      return {
        connection,
        period: undefined,
        endDate: undefined,
        utrCodes: codes,
        startDate: undefined,
        isCompleted: true
      };
    }

    it('should skip invalid connections', async () => {
      const invalidConnection = {
        _id: new ObjectId(),
        initiativeId: new ObjectId(),
        integrationCode: 'greenly',
        status: 'pending',
        data: {},
        createdBy: new ObjectId()
      } as IntegrationConnectionPlain;

      const data = await greenlyService.generateIntegrationData(await getDataLookup(invalidConnection));
      expect(data).to.be.an('array').that.is.empty;
    });

    it('should return data with emissions', async () => {
      sandbox.stub(greenlyService, 'useMockData' as any).value(false);
      sandbox.stub(greenlyApi, 'getCompany').resolves(greenlyCompany as GreenlyCompany);
      sandbox.stub(greenlyApi, 'getEmissionsDataTotal').resolves(emissionData);

      const connection = getGreenlyConnection(initiativeOneSimple._id) as IntegrationConnectionPlain<{ externalId: string }>;
      connection.status = 'active';
      connection.data.externalId = 'company-123';

      const dataLookup = await getDataLookup(connection);
      const data = await greenlyService.generateIntegrationData(dataLookup);

      expect(data).to.be.an('array').that.is.not.empty;
      const { utr, utrvs, integrationCode } = data[0];
      expect(integrationCode).eq(greenlyService.code);
      expect(utr).to.have.property('code');
      expect(utrvs).to.be.an('array');
    });

    it('should use mock data when configured', async () => {
      sandbox.stub(greenlyService, 'useMockData' as any).value(true);

      const connection = getGreenlyConnection(initiativeOneSimple._id) as IntegrationConnectionPlain<{ externalId: string }>;
      connection.status = 'active';
      connection.data.externalId = 'company-123';

      const dataLookup = await getDataLookup(connection);
      const data = await greenlyService.generateIntegrationData(dataLookup);

      expect(data).to.be.an('array').that.is.not.empty;
      // No API calls should be made when using mock data
      expect(sandbox.mock(greenlyApi).expects('getCompany').never());
      expect(sandbox.mock(greenlyApi).expects('getEmissionsDataTotal').never());
    });

    it('should handle api error', async () => {
      const errorMsg = '__API_TEST_ERROR__';
      sandbox.stub(greenlyService, 'useMockData' as any).value(false);
      sandbox.stub(greenlyApi, 'getEmissionsDataTotal')
        .onFirstCall().rejects(new ContextError(errorMsg))
        .resolves([])

      const connection = getGreenlyConnection(initiativeOneSimple._id) as IntegrationConnectionPlain<{ externalId: string }>;
      connection.status = 'active';
      connection.data.externalId = 'company-123';

      const loggerSpy = sandbox.spy(testLogger, 'warn');
      const dataLookup = await getDataLookup(connection);
      await greenlyService.generateIntegrationData(dataLookup);

      expect(loggerSpy.callCount).eq(1);
      const error = loggerSpy.getCall(0).args[0];
      expect(error).to.be.instanceOf(ContextError);
      if (error instanceof ContextError) {
        expect(error.message).to.contain('Failed to fetch Greenly emissions data');
        expect(error.context?.initiativeId).to.eq(initiativeOneSimple._id.toString());
      }
    });
  });

  describe('getExternalApp', () => {
    it('should return external app info', async () => {
      const appInfo = await greenlyService.getExternalApp();
      expect(appInfo).to.have.property('name', 'Greenly');
      expect(appInfo).to.have.property('login');
      expect(appInfo.login).to.have.property('url');
      expect(appInfo.login).to.have.property('text', 'Login to Greenly');
    });
  });
});

