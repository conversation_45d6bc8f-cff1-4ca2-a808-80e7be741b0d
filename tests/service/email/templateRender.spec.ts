/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import initial, { EmailTemplate } from '../../../server/service/email/templates/onboarding/initial';
import { userOne } from '../../fixtures/userFixtures';
import { expect } from 'chai';
import { asyncRequestContext, RequestContext } from '../../../server/middleware/audit/contextMiddleware';
import { DomainConfig, domainConfig } from '../../../server/service/organization/domainConfig';
import { followUpEmail } from '../../../server/service/email/templates/onboarding/followUpEmail';
import { ObNotificationCode } from '../../../server/models/onboarding';
import activationRequired from '../../../server/service/email/templates/user/activationRequired';
import { SGXESGenome } from '../../../server/service/app/company-tracker/SGXESGenome';
import { getAppConfigService } from '../../../server/service/app/AppConfigService';
import config from '../../../server/config';

describe('Render template tests', function () {
  const appConfigService = getAppConfigService();

  const [sgxDomainConfig] = domainConfig;
  const origin = 'https://sg.staging.g17.eco';
  const welcomeText = 'Welcome to the Singapore ESG Disclosure Platform.';

  describe('activation required', () => {
    const sgxSubject = 'SGX ESGenome Disclosure Portal - activation required';

    function verifySgxTemplate(subject: string, topContent: string) {
      expect(subject).to.be.eq(sgxSubject);
      expect(topContent).to.contain(welcomeText);
      expect(topContent).to.contain(
        'Please click on the button below to activate your account, and access the platform.'
      );
      expect(topContent).to.contain('Thank you');
    }

    function verifyDefaultTemplate(subject: string, topContent: string) {
      expect(subject).to.be.eq('G17Eco - activation required');
      expect(topContent).to.contain(
        'Thank you for registering. Please activate your account by clicking the button below.'
      );
    }

    it('should render initial template with context', () => {
      const testFn = () => {
        const { subject, topContent } = activationRequired({
          domain: undefined,
          domainConfig: undefined,
          appConfig: undefined,
        });
        verifySgxTemplate(subject, topContent);
      };
      asyncRequestContext.run({ domainConfig: sgxDomainConfig } as RequestContext, testFn);
    });

    it('should render with empty context', () => {
      const testFn = () => {
        const { subject, topContent } = activationRequired({
          appConfig: undefined,
          domain: 'unknown',
          domainConfig: sgxDomainConfig,
        });
        verifySgxTemplate(subject, topContent);
      };
      asyncRequestContext.run({} as RequestContext, testFn);
    });

    it('should render with appConfig', () => {
      const testFn = () => {
        const { subject, topContent } = activationRequired({
          appConfig: SGXESGenome,
          domain: undefined,
          domainConfig: undefined,
        });
        verifySgxTemplate(subject, topContent);
      };
      asyncRequestContext.run({} as RequestContext, testFn);
    });

    it('should prioritize appConfig, rather than domainConfig', async () => {
      const defaultCtlConfig = await appConfigService.mustGetByOnboardingPath('company-tracker');
      const testFn = () => {
        const { subject, topContent } = activationRequired({
          appConfig: defaultCtlConfig,
          domain: undefined,
          domainConfig: sgxDomainConfig,
        });
        verifyDefaultTemplate(subject, topContent);
      };
      asyncRequestContext.run({} as RequestContext, testFn);
    });
  });

  describe('initial email', () => {
    const data = {
      domain: undefined,
      domainConfig: undefined,
      appConfig: undefined,
      firstName: userOne.firstName,
      onboardingUrl: '',
      unsubscribeUrl: '',
      initiativeName: 'World Better Place',
      emailTemplate: EmailTemplate.SurveyContributorVerifierAssurer,
    };

    it('should render initial template with context', function () {
      const testFn = () => {
        const { subject, body } = initial(data);
        expect(subject).to.be.eq('Singapore ESG Disclosure Platform - Member Registration');
        expect(body).to.contain(sgxDomainConfig.logoUrl);
        expect(body).to.contain(welcomeText);
        expect(body).to.contain('Please click on the button below to register, and access the platform.');
      };

      asyncRequestContext.run(
        {
          origin,
          domainConfig: sgxDomainConfig,
        } as RequestContext,
        testFn
      );
    });

    it('should render initial G17Eco template', function () {
      const { subject, body } = initial(data);
      expect(subject).to.be.eq('G17Eco Member Registration');
      expect(body).to.contain(config.assets.defaultLogo);
    });

    it('should render origin passed from context', function () {
      const testFn = () => {
        const { subject, body } = initial(data);
        expect(subject).to.be.eq('G17Eco Member Registration');
        expect(body).to.contain(`${origin}/legal-privacy-policy`);
      };

      asyncRequestContext.run({ origin } as RequestContext, testFn);
    });

    it('should render origin passed from data, as priority', function () {
      const testFn = () => {
        const domain = 'http://localhost:4001';
        const { subject, body } = initial({ ...data, domain: domain });
        expect(subject).to.be.eq('G17Eco Member Registration');
        expect(body).to.contain(`${domain}/legal-privacy-policy`);
      };

      asyncRequestContext.run({ origin } as RequestContext, testFn);
    });
  });

  describe('follow up email', () => {
    const data = {
      appConfig: undefined,
      domain: undefined,
      domainConfig: undefined,
      firstName: userOne.firstName,
      onboardingUrl: `${origin}/ob-url`,
      unsubscribeUrl: '',
      initiativeName: 'World Better Place',
    };

    describe('follow first', function () {
      const firstData = { ...data, action: ObNotificationCode.FirstReminder };

      it('should render first reminder template', function () {
        const { subject, body } = followUpEmail({
          ...firstData,
          domain: origin,
          domainConfig: sgxDomainConfig,
        });

        // Running without context, expect to use followUp data
        expect(subject).to.be.eq('Singapore ESG Disclosure Platform - Registration reminder');
        expect(body).to.contain('on the Singapore ESG Disclosure Platform.');
        expect(body).to.contain(data.onboardingUrl);
        expect(body).to.contain(sgxDomainConfig.logoUrl);
        expect(body).to.contain('Thank you');
      });

      it('should render first reminder template app config', function () {
        const { body } = followUpEmail({
          ...firstData,
          appConfig: SGXESGenome,
          domain: origin,
          domainConfig: {} as DomainConfig,
        });

        // Running without context, expect to use followUp data
        expect(body).to.contain(data.onboardingUrl);
        expect(body).to.contain(sgxDomainConfig.logoUrl);
      });

      it('should render first reminder template for G17 Eco', function () {
        const { subject, body } = followUpEmail({ ...firstData });

        // Running without context, expect to use followUp data
        expect(subject).to.be.eq('G17Eco - Member Registration Reminder');
        expect(body).to.contain('reporting on the G17Eco platform.');
        expect(body).to.contain(data.onboardingUrl);
      });
    });

    describe('follow up final', function () {
      const finalData = { ...data, action: ObNotificationCode.FinalReminder };

      it('should render sgx final', function () {
        const { subject, body } = followUpEmail({
          ...finalData,
          domain: origin,
          domainConfig: sgxDomainConfig,
        });

        // Running without context, expect to use followUp data
        expect(subject).to.be.eq('Singapore ESG Disclosure Platform - Registration Final reminder');
        expect(body).to.contain(data.onboardingUrl);
        expect(body).to.contain('You were recently invited by <strong>');
        expect(body).to.contain(sgxDomainConfig.logoUrl);
        expect(body).to.contain('Thank you');
      });
    });
  });
});
