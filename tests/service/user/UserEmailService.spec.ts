/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import * as sinon from 'sinon';
import * as chai from 'chai';

import EmailTransaction from '../../../server/service/email/model/EmailTransaction';
import { UserEmailService } from '../../../server/service/user/UserEmailService';
import { userOne, userTokenOne } from '../../fixtures/userFixtures';
import { MockMailer } from '../../mocks/MockMailer';
import { onboardingOne } from '../../fixtures/onboardingFixtures';
import { domainConfig } from '../../../server/service/organization/domainConfig';

describe('UserEmail service', () => {
  const sandbox = sinon.createSandbox();
  const manager = new UserEmailService(new MockMailer());

  describe('UserManagerService', () => {
    const activationData = {
      user: userOne,
      isManager: true,
      domain: undefined,
      domainConfig: undefined,
      appConfig: undefined,
    };

    // Create empty context to avoid logging errors
    const [sgxDomainConfig] = domainConfig;

    let createStub: sinon.SinonStub;
    before(() => {
      createStub = sandbox.stub(EmailTransaction, 'create');
      createStub.callsFake(() => Promise.resolve());
    });
    afterEach(() => createStub.resetHistory());
    after(() => sandbox.restore());

    it('UserEmail sendActivatedEmail', async () => {
      const resp = await manager.sendActivatedEmail(activationData);
      chai.expect(createStub.calledOnce).to.be.equal(true);
      chai.expect(resp.getId()).to.be.equal(MockMailer.defaultMessageId);
    });

    it('UserEmail manager sendActivatedEmail', async () => {
      const resp = await manager.sendActivatedEmail(activationData);
      chai.expect(createStub.calledOnce).to.be.equal(true);
      chai.expect(resp.getId()).to.be.equal(MockMailer.defaultMessageId);
    });

    it('UserEmail sendActivationRequiredEmail', async () => {
      const resp = await manager.sendActivationRequiredEmail({
        user: userOne,
        activationPath: 'http://localhost:4001/activate',
        userToken: userTokenOne,
        domain: undefined,
        domainConfig: sgxDomainConfig,
        appConfig: undefined,
      });
      chai.expect(createStub.calledOnce).to.be.equal(true);
      chai.expect(resp.getId()).to.be.equal(MockMailer.defaultMessageId);
    });

    it('UserEmail sendOnboardingActivatedEmail', async () => {
      const resp = await manager.sendOnboardingActivatedEmail(onboardingOne, {
        ...activationData,
        isManager: false,
      });
      chai.expect(resp.getId()).to.be.equal(MockMailer.defaultMessageId);
    });

    it('UserEmail manager sendOnboardingActivatedEmail', async () => {
      const resp = await manager.sendOnboardingActivatedEmail(onboardingOne, {
        ...activationData,
        isManager: true,
      });
      chai.expect(resp.getId()).to.be.equal(MockMailer.defaultMessageId);
    });
  });
});
