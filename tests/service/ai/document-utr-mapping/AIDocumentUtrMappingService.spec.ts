import { getChatGPT } from '../../../../server/service/ai/models/ChatGPT';
import { AIDocumentUtrMappingService } from '../../../../server/service/ai/document-utr-mapping/AIDocumentUtrMappingService';
import { expect } from 'chai';
import sinon from 'sinon';
import { testLogger } from '../../../factories/logger';
import { getBlueprintRepository } from '../../../../server/repository/BlueprintRepository';
import UniversalTracker from '../../../../server/models/universalTracker';
import Initiative, { InitiativeTags } from '../../../../server/models/initiative';
import { AppSetting, AppSettingKey } from '../../../../server/models/app-setting';
import { DocumentUtrMapping } from '../../../../server/models/documentUniversalTrackerMapping';
import { createDocument } from '../../../fixtures/documentFixtures';
import { DocumentOwnerType } from '../../../../server/models/document';
import { initiativeOneSimple } from '../../../fixtures/initiativeFixtures';
import { createMongooseModel } from '../../../setup';
import { FeatureTag } from '@g17eco/core';
import { getStorage } from '../../../../server/service/storage/fileStorage';
import { FileObject } from 'openai/resources/files';
import * as fs from 'fs';
import * as file from '../../../../server/service/file/file';
import OpenAI from 'openai';

describe('AIDocumentUtrMappingService', () => {
  const sandbox = sinon.createSandbox();
  const aiModel = getChatGPT();
  const blueprintRepo = getBlueprintRepository();
  const fileStorage = getStorage();
  const fileSystem = {
    createReadStream: sandbox.stub(),
    unlinkSync: sandbox.stub(),
  };

  const ownerId = initiativeOneSimple._id;
  const document = createDocument({ ownerType: DocumentOwnerType.Initiative, ownerId });

  const service = new AIDocumentUtrMappingService(
    aiModel,
    DocumentUtrMapping,
    AppSetting,
    testLogger,
    UniversalTracker,
    blueprintRepo,
    Initiative,
    fileStorage,
    fileSystem
  );

  beforeEach(() => {
    fileSystem.createReadStream.returns({} as fs.ReadStream);
    fileSystem.unlinkSync.returns(undefined);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('mapRelevantUtrs', () => {
    describe('Document cannot be mapped', () => {
      it(`should return empty array if document ownerType is not ${DocumentOwnerType.Initiative}`, async () => {
        const result = await service.mapRelevantUtrs(createDocument({ ownerType: DocumentOwnerType.Survey }));
        expect(result).to.deep.equal([]);
      });

      it(`should return empty array if document doesn't have ownerId`, async () => {
        const result = await service.mapRelevantUtrs(
          createDocument({ ownerType: DocumentOwnerType.Initiative, ownerId: undefined })
        );
        expect(result).to.deep.equal([]);
      });

      it(`should return empty array if cannot find initiative by ownerId`, async () => {
        sandbox.stub(Initiative, 'findById').returns(createMongooseModel(undefined));

        const result = await service.mapRelevantUtrs(document);
        expect(result).to.deep.equal([]);
      });

      it(`should return empty array if document initiative owner is not organization`, async () => {
        sandbox.stub(Initiative, 'findById').returns(createMongooseModel(initiativeOneSimple));

        const result = await service.mapRelevantUtrs(document);
        expect(result).to.deep.equal([]);
      });

      it(`should return empty array if organization hasn't enable ai document features`, async () => {
        sandbox.stub(Initiative, 'findById').returns(
          createMongooseModel({
            ...initiativeOneSimple,
            tags: [InitiativeTags.Organization],
          })
        );

        const result = await service.mapRelevantUtrs(document);
        expect(result).to.deep.equal([]);
      });
    });

    describe('Document can be mapped', () => {
      beforeEach(() => {
        sandbox.stub(Initiative, 'findById').returns(
          createMongooseModel({
            ...initiativeOneSimple,
            tags: [InitiativeTags.Organization, FeatureTag.AIAccessDocumentLibrary],
          })
        );
      });

      it('should return mapped utrs if document has been mapped', async () => {
        const utrs = [{ code: 'utr-code', score: 1 }];
        sandbox.stub(DocumentUtrMapping, 'findOne').returns(createMongooseModel({ utrs }));

        const result = await service.mapRelevantUtrs(document);
        expect(result).to.deep.equal(utrs);
      });

      describe('Document has not been mapped', () => {
        const utrs = [{ code: 'utr-code', score: 1 }];
        const assistantId = 'assistant-id';
        const utrsFileId = 'utrs-file-id';
        const uploadedDocumentId = 'uploaded-document-id';
        let runThreadWithAssistantStub: sinon.SinonStub;
        let deleteFileStub: sinon.SinonStub;

        beforeEach(() => {
          sandbox.stub(DocumentUtrMapping, 'findOne').returns(createMongooseModel(undefined));
          // Stub uploadDocument
          sandbox.stub(fileStorage, 'getSignedUrl').resolves(['signed-url']);
          sandbox.stub(file, 'downloadFile').resolves(undefined);

          runThreadWithAssistantStub = sandbox.stub(aiModel, 'runThreadWithAssistant').resolves({ result: utrs });
          sandbox.stub(DocumentUtrMapping, 'create').resolves(undefined);
          deleteFileStub = sandbox.stub(aiModel, 'deleteFile').resolves(undefined);
        });

        describe('Utrs file does not exist', () => {
          it('should create utrs file', async () => {
            sandbox.stub(AppSetting, 'find').returns(
              createMongooseModel([
                {
                  key: AppSettingKey.OpenAIRelevantUTRsDocumentScanAssistantId,
                  value: assistantId,
                },
              ])
            );
            sandbox.stub(UniversalTracker, 'find').returns(createMongooseModel([]));
            sandbox
              .stub(aiModel, 'createFile')
              .onFirstCall()
              .resolves({ id: uploadedDocumentId } as FileObject)
              .onSecondCall()
              .resolves({ id: utrsFileId } as FileObject);
            const appSettingModelCreateStub = sandbox.stub(AppSetting, 'create').resolves(undefined);

            await service.mapRelevantUtrs(document);

            expect(
              appSettingModelCreateStub.calledOnceWithExactly({
                key: AppSettingKey.OpenAIUTRsFileId,
                value: utrsFileId,
              })
            ).to.be.true;
          });
        });

        describe('Utrs file exists', () => {
          beforeEach(() => {
            sandbox
              .stub(aiModel, 'createFile')
              .onFirstCall()
              .resolves({ id: uploadedDocumentId } as FileObject);
          });

          it('should create assistant if not exist', async () => {
            const createAssistantStub = sandbox
              .stub(aiModel, 'createAssistant')
              .resolves({ id: assistantId } as OpenAI.Beta.Assistants.Assistant);
            sandbox.stub(AppSetting, 'find').returns(
              createMongooseModel([
                {
                  key: AppSettingKey.OpenAIUTRsFileId,
                  value: utrsFileId,
                },
              ])
            );
            const appSettingModelCreateStub = sandbox.stub(AppSetting, 'create').resolves(undefined);

            await service.mapRelevantUtrs(document);

            expect(createAssistantStub.calledOnce).to.be.true;
            expect(
              appSettingModelCreateStub.calledOnceWithExactly({
                key: AppSettingKey.OpenAIRelevantUTRsDocumentScanAssistantId,
                value: assistantId,
              })
            ).to.be.true;
          });

          describe('Assistant exists', () => {
            beforeEach(() => {
              sandbox.stub(AppSetting, 'find').returns(
                createMongooseModel([
                  {
                    key: AppSettingKey.OpenAIRelevantUTRsDocumentScanAssistantId,
                    value: assistantId,
                  },
                  {
                    key: AppSettingKey.OpenAIUTRsFileId,
                    value: utrsFileId,
                  },
                ])
              );
            });

            it('should ask AI to map document using uploaded document and utrs file', async () => {
              const result = await service.mapRelevantUtrs(document);
              expect(result).to.deep.equal(utrs);

              const runThreadWithAssistantStubArgs = runThreadWithAssistantStub.firstCall.firstArg;
              expect(runThreadWithAssistantStubArgs.assistantId).to.equal(assistantId);
              expect(runThreadWithAssistantStubArgs.message.attachments[0].file_id).to.equal(utrsFileId);
              expect(runThreadWithAssistantStubArgs.message.attachments[1].file_id).to.equal(uploadedDocumentId);

              expect(deleteFileStub.firstCall.firstArg).to.equal(uploadedDocumentId);
            });
          });
        });
      });
    });
  });
});
