/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import * as sinon from 'sinon';
import * as chai from 'chai';
import {
  onboardingOne,
  onboardingSurveyConfig,
} from '../../fixtures/onboardingFixtures';
import Onboarding, { NotificationItem, ObNotificationCode, OnboardingStatus } from '../../../server/models/onboarding';
import { OnboardingEmailService } from '../../../server/service/onboarding/OnboardingEmailService';
import { MockMailer } from '../../mocks/MockMailer';
import EmailTransaction
, { EmailDeliverySubType,
EmailDeliveryType }  from '../../../server/service/email/model/EmailTransaction';
import { userOne } from '../../fixtures/userFixtures';
import SesResponse from '../../../server/service/email/SES/SesResponse';
import { CreateCrmOnboardingData } from '../../../server/service/crm/contact';
import { EmailTemplate } from '../../../server/service/email/templates/onboarding/initial';
import { getDomainConfigRepository } from '../../../server/service/organization/DomainConfigRepository';
import { getAppConfigService } from '../../../server/service/app/AppConfigService';
import { getEmailTransactionService } from '../../../server/service/email/EmailTransactionService';
import BadRequestError from '../../../server/error/BadRequestError';
import { subtractDate } from '../../../server/util/date';
import { OnboardingEmailMessage } from '../../../server/service/onboarding/constants';
import { initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';
import { createMongooseModel } from '../../setup';
import ContextError from '../../../server/error/ContextError';

describe('OnboardingEmailService', () => {
  const sandbox = sinon.createSandbox();

  const emailResp = new SesResponse({ MessageId: '12312312312312123' });
  const emailTransactionService = getEmailTransactionService();
  const onboardingEmailService = new OnboardingEmailService(
    new MockMailer(emailResp),
    getDomainConfigRepository(),
    getAppConfigService(),
    emailTransactionService
  );

  let emailTransactionModel: sinon.SinonStub;

  before(() => {
    emailTransactionModel = sandbox.stub(EmailTransaction, 'create');
    emailTransactionModel.callsFake(() => Promise.resolve());
  });

  after(() => sandbox.restore());

  describe('userDecision', () => {
    describe('acceptOnboarding', () => {
      beforeEach(() => emailTransactionModel.resetHistory());

      it('New User created email', async () => {
        const result = await onboardingEmailService.sendUserCreatedToSupport(
          {
            ...onboardingOne,
            status: OnboardingStatus.Complete,
            surveyConfig: onboardingSurveyConfig,
          },
          userOne
        );
        chai.expect(result.getId()).to.be.equal(emailResp.getId());
        chai.assert(emailTransactionModel.calledOnce);
      });
    });

    describe('sendOnboardingInitial', () => {
      beforeEach(() => emailTransactionModel.resetHistory());
      const crmData: CreateCrmOnboardingData = {
        contactId: '',
        externalId: '',
        appConfigCode: undefined,
        initiativeName: 'Test company',
        onboardingUrl: '',
        token: '',
        type: '',
        unsubscribeUrl: '',
      };

      it('should send email', async () => {
        const result = await onboardingEmailService.sendOnboardingInitial(
          { ...onboardingOne, status: OnboardingStatus.Pending },
          crmData
        );
        chai.expect(result.getId()).to.be.equal(emailResp.getId());
        chai.assert(emailTransactionModel.calledOnce);
      });

      it('should send email for admin user', async () => {
        const result = await onboardingEmailService.sendOnboardingInitial(
          {
            ...onboardingOne,
            user: { ...onboardingOne.user, emailTemplate: EmailTemplate.Manager },
            status: OnboardingStatus.Pending,
          },
          crmData
        );
        chai.expect(result.getId()).to.be.equal(emailResp.getId());
        chai.assert(emailTransactionModel.calledOnce);
      });
    });
  });

  describe('getSentEmails', () => {
    afterEach(() => {
      sandbox.restore();
    });

    it('should return empty array with empty notification items', async () => {
      sandbox.stub(emailTransactionService, 'findAllByIds').resolves([]);
      sandbox.stub(Onboarding, 'findOne').returns(createMongooseModel(onboardingOne));
      const result = await onboardingEmailService.getSentEmails({
        onboardingId: onboardingOne._id,
        initiativeId: initiativeOneSimpleId,
      });
      chai.expect(result).eqls([]);
    });

    it('should return emails sent with their statuses', async () => {
      const onboarding = new Onboarding({
        ...onboardingOne,
        notifications: {
          items: [
            {
              code: ObNotificationCode.FirstReminder,
              completedDate: new Date(),
              type: 'email',
              referenceId: 'id-1',
            },
            {
              code: ObNotificationCode.FinalReminder,
              completedDate: new Date(),
              type: 'email',
              referenceId: 'id-2',
            },
          ],
        },
      });
      sandbox.stub(emailTransactionService, 'findAllByIds').resolves([
        {
          id: 'transaction-id-1',
          externalId: 'id-1',
          type: EmailDeliveryType.Delivery,
        },
        {
          id: 'transaction-id-2',
          externalId: 'id-2',
          type: EmailDeliveryType.Delivery,
        },
      ] as EmailTransaction[]);
      sandbox.stub(Onboarding, 'findOne').returns(createMongooseModel(onboarding));

      const result = await onboardingEmailService.getSentEmails({
        onboardingId: onboarding._id,
        initiativeId: initiativeOneSimpleId,
      });
      chai.expect(result).to.eqls([
        {
          onboardingId: onboarding._id,
          externalId: 'id-1',
          event: ObNotificationCode.FirstReminder,
          id: 'transaction-id-1',
          type: EmailDeliveryType.Delivery,
          subType: undefined,
          createdAt: undefined,
          updatedAt: undefined,
        },
        {
          onboardingId: onboarding._id,
          externalId: 'id-2',
          event: ObNotificationCode.FinalReminder,
          id: 'transaction-id-2',
          type: EmailDeliveryType.Delivery,
          subType: undefined,
          createdAt: undefined,
          updatedAt: undefined,
        },
      ]);
    });

    it('should return emails sent without statuses', async () => {
      const onboarding = new Onboarding({
        ...onboardingOne,
        notifications: {
          items: [
            {
              code: ObNotificationCode.FirstReminder,
              completedDate: new Date(),
              type: 'email',
              referenceId: 'id-1',
            },
            {
              code: ObNotificationCode.FinalReminder,
              completedDate: new Date(),
              type: 'email',
              referenceId: 'id-2',
            },
          ],
        },
      });
      sandbox.stub(emailTransactionService, 'findAllByIds').resolves([]);
      sandbox.stub(Onboarding, 'findOne').returns(createMongooseModel(onboarding));

      const result = await onboardingEmailService.getSentEmails({
        onboardingId: onboarding._id,
        initiativeId: initiativeOneSimpleId,
      });

      chai.expect(result).to.eqls([
        {
          onboardingId: onboarding._id,
          externalId: 'id-1',
          event: ObNotificationCode.FirstReminder,
        },
        {
          onboardingId: onboarding._id,
          externalId: 'id-2',
          event: ObNotificationCode.FinalReminder,
        },
      ]);
    });
  });

  describe('ensureAllowedToResend', () => {
    afterEach(() => {
      sandbox.restore();
    });

    it('should throw an error when not all reminders have been sent', async () => {
      const onboarding = new Onboarding({
        ...onboardingOne,
        notifications: {
          items: [
            {
              code: ObNotificationCode.FirstReminder,
            },
          ],
        },
      });
      await chai
        .expect(onboardingEmailService.ensureAllowedToResend(onboarding))
        .to.eventually.be.rejectedWith(BadRequestError, OnboardingEmailMessage.AutoReminderNotElapsed);
    });

    it('should throw an error when no transactions are found', async () => {
      const onboarding = new Onboarding({
        ...onboardingOne,
        notifications: {
          items: [
            {
              code: ObNotificationCode.FirstReminder,
            },
            {
              code: ObNotificationCode.FinalReminder,
            },
          ],
        },
      });
      sandbox.stub(emailTransactionService, 'findAllByIds').resolves([]);
      await chai
        .expect(onboardingEmailService.ensureAllowedToResend(onboarding))
        .to.eventually.be.rejectedWith(BadRequestError, OnboardingEmailMessage.AutoReminderNotElapsed);
    });

    it('should throw an error when a hard bounce is detected', async () => {
      const onboarding = new Onboarding({
        ...onboardingOne,
        notifications: {
          items: [
            {
              code: ObNotificationCode.FirstReminder,
            },
            {
              code: ObNotificationCode.FinalReminder,
            },
          ],
        },
      });
      sandbox.stub(emailTransactionService, 'findAllByIds').resolves([
        {
          type: EmailDeliveryType.Bounce,
          subType: EmailDeliverySubType.Permanent,
        },
      ] as EmailTransaction[]);
      await chai
        .expect(onboardingEmailService.ensureAllowedToResend(onboarding))
        .to.eventually.be.rejectedWith(BadRequestError, OnboardingEmailMessage.EmailDeliveryFailure);
    });

    it('should throw an error when a reminder has been sent recently', async () => {
      const onboarding = new Onboarding({
        ...onboardingOne,
        notifications: {
          items: [
            {
              code: ObNotificationCode.FirstReminder,
              completedDate: new Date(),
              type: 'email',
              referenceId: 'id-1',
            },
            {
              code: ObNotificationCode.FinalReminder,
              completedDate: new Date(),
              type: 'email',
              referenceId: 'id-2',
            },
            {
              code: ObNotificationCode.ManualReminder,
              completedDate: subtractDate(new Date(), 30, 'minutes'),
              type: 'email',
              referenceId: 'id-3',
            },
          ],
        },
      });
      sandbox.stub(emailTransactionService, 'findAllByIds').resolves([
        {
          id: 'transaction-id-1',
          externalId: 'id-1',
          type: EmailDeliveryType.Delivery,
        },
        {
          id: 'transaction-id-2',
          externalId: 'id-2',
          type: EmailDeliveryType.Delivery,
        },
        {
          id: 'transaction-id-3',
          externalId: 'id-3',
          type: EmailDeliveryType.Delivery,
        },
      ] as EmailTransaction[]);
      await chai
        .expect(onboardingEmailService.ensureAllowedToResend(onboarding))
        .to.eventually.be.rejectedWith(BadRequestError, OnboardingEmailMessage.CooldownPeriodActive);
    });

    it('should not throw an error when all conditions are met', async () => {
      const onboarding = new Onboarding({
        ...onboardingOne,
        notifications: {
          items: [
            {
              code: ObNotificationCode.FirstReminder,
              completedDate: new Date(),
              type: 'email',
              referenceId: 'id-1',
            },
            {
              code: ObNotificationCode.FinalReminder,
              completedDate: new Date(),
              type: 'email',
              referenceId: 'id-2',
            },
          ],
        },
      });
      sandbox.stub(emailTransactionService, 'findAllByIds').resolves([
        {
          id: 'transaction-id-1',
          externalId: 'id-1',
          type: EmailDeliveryType.Delivery,
        },
        {
          id: 'transaction-id-2',
          externalId: 'id-2',
          type: EmailDeliveryType.Delivery,
        },
      ] as EmailTransaction[]);
      await chai.expect(onboardingEmailService.ensureAllowedToResend(onboarding)).to.not.be.rejected;
    });
  });

  describe('sendManualReminder', () => {
    let ensureAllowedToResendStub: sinon.SinonStub;
    let handleFollowUpEmailStub: sinon.SinonStub;

    beforeEach(() => {
      ensureAllowedToResendStub = sandbox.stub(onboardingEmailService, 'ensureAllowedToResend');
      handleFollowUpEmailStub = sandbox.stub(onboardingEmailService, 'handleFollowUpEmail');
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should throw an error when onboardingId is not found', async () => {
      const onboardingModel = createMongooseModel(null);
      onboardingModel.exec = () => {
        throw new ContextError('Mongo error');
      };
      sandbox.stub(Onboarding, 'findOne').returns(onboardingModel);
      await chai
        .expect(
          onboardingEmailService.sendManualReminder({
            onboardingId: onboardingOne._id,
            initiativeId: initiativeOneSimpleId,
          })
        )
        .to.eventually.be.rejectedWith(ContextError, /Mongo error/);
    });

    it('should throw an error when ensureAllowedToResend fails', async () => {
      sandbox.stub(Onboarding, 'findOne').returns(createMongooseModel(onboardingOne));
      ensureAllowedToResendStub.rejects(new BadRequestError('Not allowed'));
      await chai
        .expect(
          onboardingEmailService.sendManualReminder({
            onboardingId: onboardingOne._id,
            initiativeId: initiativeOneSimpleId,
          })
        )
        .to.eventually.be.rejectedWith(BadRequestError, /Not allowed/);
    });

    it('should call handleFollowUpEmail with the correct parameters', async () => {
      const notificationItem: NotificationItem = {
        code: ObNotificationCode.ManualReminder,
        completedDate: new Date(),
        type: 'email',
        referenceId: 'test-id',
      };
      sandbox.stub(Onboarding, 'findOne').returns(createMongooseModel(onboardingOne));
      ensureAllowedToResendStub.resolves();
      handleFollowUpEmailStub.resolves(notificationItem);
      const result = await onboardingEmailService.sendManualReminder({
        onboardingId: onboardingOne._id,
        initiativeId: initiativeOneSimpleId,
      });
      chai.expect(handleFollowUpEmailStub.calledOnceWith(onboardingOne, ObNotificationCode.ManualReminder)).to.be.true;
      chai.expect(result).to.eqls(notificationItem);
    });
  });
});
